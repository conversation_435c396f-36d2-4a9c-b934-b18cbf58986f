<?php

class initGift
{
    function __construct()
    {

    }

    function get()
    {
        return array('data' => 1);
    }

    function getlist()
    {



        $brand_id = Url::getParamInt('brand_id', 0);
        $city_id = Url::getParamInt('city_id', 0);
        $cat_id = Url::getParamInt('cat_id', 0);
        $is_hot = Url::getParamInt('is_hot', 0);
        $per_page = Url::getParamInt('per_page', 15);
        $page_no = Url::getParamInt('page_no', 1);

        $id_gift_set = Url::getParamInt('id_gift_set', 0);

        $genkeyCache = 'GETLIST2.0'.md5($brand_id.$city_id.$cat_id.$is_hot.$per_page.$id_gift_set.$page_no);
        $dataReturn = CacheLib::get($genkeyCache);
        if(empty($dataReturn)) {
            #Phục vu giao dien V2 App
            $textTitle = 'Quà tặng';
            $avatarTitle = '';
            if($brand_id>0){
                $brand = Brand::get($brand_id);
                if(!empty($brand)){
                    $textTitle = $brand['title'];
                    $avatarTitle = $brand['image_src'];
                }
            }
            if($cat_id>0){
                $cat = Category::get($cat_id);
                if(!empty($cat)){
                    $textTitle = $cat['title'];
                    $image = MediaUrl::fromImageTitle($cat['avatar']);
                    $avatarTitle = isset($image[640])?$image[640]:'';
                }
            }
            if($is_hot>0){
                $textTitle = 'Quà tặng nổi bật';
            }
            ####End####

            if ($cat_id > 0) {
                $param['cat_id'] = $cat_id;
            }
            if ($city_id > 0) {
                $param['city_id'] = $city_id;
            }
            if ($is_hot == 1) {
                $param['display'] = 1;
            }
            if ($brand_id > 0) {
                $param['brand_id'] = $brand_id;
            }

            $id_gift_detail = array();

            if($id_gift_set>0){
                #Lay them phan chi ban theo GIFT_SET
                $getSiteGift = DB::query("SELECT * FROM ".T_GIFT_SET." WHERE gift_id=".$id_gift_set);
                if($getSiteGift){
                    while ($row = @mysql_fetch_assoc($getSiteGift)){
                        $id_gift_detail[$row['gift_detail_id']] = $row['gift_detail_id'];
                    }
                    if(!empty($id_gift_detail)){
                        $id_gift_site_arr = array();
                        $query_detail = DB::query("SELECT * FROM ".T_GIFT_DETAIL." WHERE id IN (".implode(',',$id_gift_detail).")");
                        if($query_detail){
                            while ($rowDT = @mysql_fetch_assoc($query_detail)){
                                $id_gift_site_arr[$rowDT['gift_id']] = $rowDT['gift_id'];
                            }
                            $param['id_arr'] = $id_gift_site_arr;
                        }
                    }
                }
                $param['statusIn'] = array(1,2);
            }else{
                $param['status'] = 1;
            }

            $condition =  FunctionLib::addCondition(Gift::getWhere($param), true);
            $sql = "SELECT * FROM ".T_GIFT.$condition." ORDER BY id DESC";

            $re = Pagging::pager_query($sql, $per_page);
            $data = array();
            if($re){
                $arr_brand_id = array();
                $arr_detail_id = array();
                while ($row = @mysql_fetch_assoc($re)){
                    //$arrImage = (array)json_decode($row['images'], true);
                    //$row['images'] = $arrImage;
                    $temp_imgages = '';
                    /*if(!empty($arrImage)){
                        foreach ($arrImage as $k => $v) {
                            if($v!=''&&$temp_imgages == ''){
                                //$row['image_src'][$k] = ImageUrl::getImageURL($v, $row['created'], 320, 'gift', 'gift/');
                                $temp_imgages = ImageUrl::getImageURL($v, $row['created'], 320, 'gift', 'gift/');
                            }

                        }
                    }*/

                    if(isset($row['avatar'])){
                        $images = MediaUrl::fromImageTitle($row['avatar']);
                        $temp_imgages = isset($images['640'])?$images['640']:'';
                    }
                    $temp = array(
                        'id'=>$row['id'],
                        'brand_id'=>$row['brand_id'],
                        'cat_id'=>$row['cat_id'],
                        'name'=>StringLib::post_db_parse_html($row['name']),
                        'view'=>$row['view'],
                        'images_src'=>$temp_imgages,
                    );
                    $arr_brand_id[$row['brand_id']] = $row['brand_id'];
                    $arr_detail_id[$row['id']] = $row['id'];
                    $data[] = $temp;
                }
                if(!empty($arr_brand_id)){
                    $re2 = DB::query("SELECT * FROM ".T_BRAND." WHERE id IN(".implode(',',$arr_brand_id).")");
                    if($re2){
                        while ($row2 = @mysql_fetch_assoc($re2)){
                            foreach ($data as &$value){
                                if($value['brand_id']==$row2['id']){
                                    $value['brand'] = array('id'=>$row2['id'],'title'=>$row2['title']);
                                }
                            }
                        }
                    }
                }

            }
            $dataReturn = array('items'=>$data,'arr_detail_id'=>$arr_detail_id,'id_gift_detail'=>$id_gift_detail,'totalPage'=>Pagging::$totalPage,'textTitle'=>$textTitle,'avatarTitle'=>$avatarTitle);
            CacheLib::set($genkeyCache, $dataReturn, 6 * 60 * 60);

        }
        if(isset($dataReturn['arr_detail_id']) && $dataReturn['arr_detail_id']){
            if($id_gift_set>0&&!empty($dataReturn['id_gift_detail'])&&$id_gift_set!=777){
                $red = DB::query("SELECT id,gift_id,code_quantity,price,title FROM ".T_GIFT_DETAIL." WHERE status=1 AND code_quantity>0 AND id IN(".implode(',',$dataReturn['id_gift_detail']).") ORDER BY position ASC");
            }else{
                $red = DB::query("SELECT id,gift_id,code_quantity,price,title FROM ".T_GIFT_DETAIL." WHERE status=1 AND code_quantity>0 AND gift_id IN(".implode(',',$dataReturn['arr_detail_id']).") ORDER BY position ASC");
            }
            if($red){
                while ($rowd = @mysql_fetch_assoc($red)){
                    foreach ($dataReturn['items'] as &$value){
                        if($value['id']==$rowd['gift_id']){
                            $value['detail'][] = array('id'=>$rowd['id'],'title'=>StringLib::post_db_parse_html($rowd['title']),'code_quantity'=>$rowd['code_quantity'],'price'=>$rowd['price'],'price_format'=>FunctionLib::priceFormat($rowd['price'],'VNĐ'));
                        }
                    }
                }
            }
        }
        #Bo nhung qua co qua tang nho hon 0
        foreach ($dataReturn['items'] as $k => $value2){
            if(!isset($value2['detail'])||(isset($value2['detail'])&&count($value2['detail'])<=0)){
                unset($dataReturn['items'][$k]);
            }
        }


        //return System::apiSuccess($data);
        return System::apiSuccess(array('items'=>array_values($dataReturn['items']),'totalPage'=>$dataReturn['totalPage'],'textTitle'=>$dataReturn['textTitle'],'avatarTitle'=>$dataReturn['avatarTitle']));
    }

    function detail()
    {
        $id = Url::getParamInt('id', 0);
        $id_gift_set = Url::getParamInt('id_gift_set', 0);

        if($id>0){
            $gift = DB::fetch("SELECT id,name,images,created,article_id,brand_id FROM ".T_GIFT." WHERE status IN (1,2) AND id=".$id);
            if(!empty($gift)){
                $id_gift_detail = array();
                if($id_gift_set>0){
                    #Lay them phan chi ban theo GIFT_SET
                    $getSiteGift = DB::query("SELECT * FROM ".T_GIFT_SET." WHERE gift_id=".$id_gift_set);
                    if($getSiteGift){
                        while ($row = @mysql_fetch_assoc($getSiteGift)){
                            $id_gift_detail[$row['gift_detail_id']] = $row['gift_detail_id'];
                        }
                    }
                }

               /* $arrImage = (array)json_decode($gift['images'], true);
                if(!empty($arrImage)){
                    foreach ($arrImage as $k => $v) {
                        if($v!=''&&!isset($gift['image_src'])){
                            $gift['image_src'] = ImageUrl::getImageURL($v, $gift['created'], 640, 'gift', 'gift/');
                        }

                    }
                }*/
                if(isset($gift['avatar'])){
                    $images = MediaUrl::fromImageTitle($gift['avatar']);
                    $gift['image_src'] = isset($images['640'])?$images['640']:'';
                }
                $gift['article'] = DB::fetch("SELECT description,note,noteB2B FROM ".T_ARTICLE." WHERE id=".$gift['article_id']);
                $gift['article']['note'] = StringLib::post_db_parse_html($gift['article']['note']);
                $gift['article']['noteB2B'] = StringLib::post_db_parse_html($gift['article']['noteB2B']);
                if($id_gift_set>0&&!empty($id_gift_detail)){
                    $re =  DB::query("SELECT * FROM ".T_GIFT_DETAIL." WHERE code_quantity>0 AND  gift_id=".$gift['id']." AND id IN(".implode(',',$id_gift_detail).") ORDER BY position ASC");
                }else{
                    $re =  DB::query("SELECT * FROM ".T_GIFT_DETAIL." WHERE code_quantity>0 AND  gift_id=".$gift['id']." ORDER BY position ASC");
                }
                if($re){
                    while ($row = @mysql_fetch_assoc($re)){
                        $gift['detail'][] = array('id'=>$row['id'],'title'=>StringLib::post_db_parse_html($row['title']),'price'=>$row['price'],'code_quantity'=>$row['code_quantity'],'price_format'=>FunctionLib::priceFormat($row['price'],'VNĐ'));
                    }
                }
                $gift['brand'] = DB::fetch("SELECT title,image,created,logo FROM ".T_BRAND." WHERE id=".$gift['brand_id']);
                if(!empty($gift['brand'])){
                    $logo = MediaUrl::fromImageTitle($gift['brand']['logo']);
                    $gift['brand']['images'] = isset($logo[640])?$logo[640]:'';
                    unset($gift['brand']['image']);
                }
                #Lay noi dung them
                $arrContent = array();
                $re2 = DB::query("SELECT id, article_id, title, content FROM ".T_ARTICLE_CONTENT." WHERE article_id = ".$gift['article_id']." AND status = 1 ORDER BY position ASC");
                if($re2){
                    while ($r = @mysql_fetch_assoc($re2)) {
                        $r['content'] = StringLib::post_db_parse_html($r['content']);
                        $arrContent[] = $r;
                    }
                }

                $gift['article']['content'] = $arrContent;
                #Lay Vi tri cua hang
                $getOffice = Brand::getOffice(array('brand_id'=>(int) $gift['brand_id'],'status'=>IS_ON,'gift_id'=> $gift['id']));
                $gift['office'] = array_values($getOffice);
                unset($gift['article_id']);
                unset($gift['images']);
                unset($gift['brand_id']);
                return System::apiSuccess($gift);
            }else{
                return System::apiError('Không tìm thấy quà');
            }
        }
        return System::apiError('notParam');
    }

    function filter(){
        $dataFiter = array();
        #Chủ đề
        $menu_cd = Category::getChild(26);
        $menu_qt = Category::getChild(2);
        $menu = array_merge($menu_qt,$menu_cd);
        $dataFiter['CATEGORIES'] = array(
            'key'=>'cat_id',
            'title'=>'Chủ đề',
            'value'=>$menu
        );
        #Thương hiêu
        $keyBrand = md5('BRAND_FITTER_KEY');
        $brand = CacheLib::get($keyBrand,86400*7);
        if(empty($brand)){
            $brand = DB::fetch_all("SELECT id,title FROM ".T_BRAND." WHERE status=".IS_ON." ORDER BY is_hot DESC");
            CacheLib::set($keyBrand,$brand,86400*7);
        }
        $dataFiter['BRANDS'] = array(
            'key'=>'brand_id',
            'title'=>'Thương hiệu',
            'value'=> array_values($brand)
        );
        #Gioi tinh
        $dataFiter['SEX'] = array(
            'key'=>'sex',
            'title'=>'Giới tính',
            'value'=> array(
                array('id'=>0,'title'=>'Nữ'),
                array('id'=>1,'title'=>'Nam'),
                array('id'=>-1,'title'=>'Cả hai')
            )
        );
        #Tỉnh thành
        $keyProvince = md5('PROVINCE_FITTER_KEY');
        $city = CacheLib::get($keyProvince,86400*7);
        if(empty($city)){
            $city = DB::fetch_all("SELECT id,title FROM ".T_PROVINCE." WHERE status=2 ORDER BY title DESC");
            CacheLib::set($keyProvince,$city,86400*7);
        }
        $dataFiter['CITY'] = array(
            'key'=>'city_id',
            'title'=>'Tỉnh thành',
            'value'=> array_values($city)
        );

        return System::apiSuccess(
            array(
                'items'=>$dataFiter
            )
        );
    }

    function item(){
        $id = Url::getParamInt('id', 0);
        $gift_detail = DB::fetch("SELECT * FROM ".T_GIFT_DETAIL." WHERE  id=".$id);
        if($gift_detail){
            if($gift_detail['status']==1){
                if($gift_detail['code_quantity']>0){
                    $content = DB::fetch("SELECT * FROM ".T_GIFT_CONTENT." WHERE lang='vi' AND gift_id=".$gift_detail['gift_id'],'content');
                    $images = MediaUrl::fromImageTitle($gift_detail['avatar']);
                    $detail = array(
                        'id'=>$gift_detail['id'],
                        'title'=>$gift_detail['title'],
                        'price'=>$gift_detail['price'],
                        'code_quantity'=>$gift_detail['code_quantity'],
                        'images'=>isset($images[640])?$images[640]:'',
                        'content'=>$content,
                        'price_format'=>FunctionLib::priceFormat($gift_detail['price'],'VNĐ'));

                    $detail['brand'] = DB::fetch("SELECT title,image,created,logo FROM ".T_BRAND." WHERE id=".$gift_detail['brand_id']);
                    if(!empty($detail['brand'])){
                        $logo = MediaUrl::fromImageTitle($detail['brand']['logo']);
                        $detail['brand']['images'] = isset($logo[640])?$logo[640]:'';
                    }
                    return System::apiSuccess($detail);
                }else{
                    return System::apiError('Quà này đã hết sản phẩm');
                }

            }
        }
        return System::apiError('Không tìm thấy quà');

    }


    /**
     * <AUTHOR> Ngọc
     * @function getGiftDetail
     * @var int $gift_detail_id
     * @var string $lang
     * @return array|string
     * */
    public function getGiftDetail()
    {
//        $gift_detail_id = Url::getParamInt('gift_detail_id');
//        $lang = Url::getParam('lang','en');
//        $data = DB::fetch_all("SELECT content,note,title from ".T_GIFT_CONTENT." WHERE gift_detail_id=$gift_detail_id AND lang='$lang'");
//        if($data) {
//            $content  = [];
//            foreach ($data as $k=> $item) {
//                $content[$k]['content'] = StringLib::post_db_parse_html($item['content']);
//                $content[$k]['note'] = StringLib::post_db_parse_html($item['note']);
//                $content[$k]['title'] = StringLib::post_db_parse_html($item['title']);
//            }
//            return System::apiSuccess($content);
//            }else{
//        return System::apiError('Không có nội dung quà này!');
//        }
    }

}