<?php

class initCart
{
    function __construct()
    {
        System::$data['status'] = FunctionLib::statusCode();
        System::$data['point_unit'] = FunctionLib::pointUnit();
    }


    function cartPay()
    {
        if(!FunctionLib::passInternal()){
            return System::apiError('Access Urbox không đúng.', ['status'=>211]);
        }
        #Kiem tra access_urbox
        $access_urbox = Url::getParam('access_urbox', '');
        $agent_site = Url::getParamInt('agent_site', 0);
        $isHash = Url::getParamInt('isHash', 0);

        $app_secret = Url::getParam('app_secret', '');
        $app_id = Url::getParamInt('app_id', 0);

        if ($app_secret != '') {
            $access_urbox = $app_secret;
        }
        if ($app_id != '') {
            $agent_site = $app_id;
        }

        if (!Site::check_access_urbox($agent_site, $access_urbox,$site)) {
            return System::apiError('Access Urbox không đúng.', ['status'=>211]);
        }

        $nopay = Url::getParamInt('nopay', 0);
        $cart_id = Url::getParamInt('cart_id', 0);

        $transaction_id = Url::getParam('transaction_id', '');
        $keyPay = Url::getParam('keyPay', '');
        $payment_status = Url::getParamInt('payment_status', 2);


        $return_url = $site['return'];
        /* Kiểm tra đầu vào nếu truyền  Signature*/
        $signature = Url::getParam('Signature', '');
        if ($site["their_key"] != '') {
            $dataSignature = array(
                'app_secret' => $access_urbox,
                'app_id' => $agent_site,
                'transaction_id' => $transaction_id,
                'payment_status' => $payment_status,
                'keyPay' => $keyPay,
                'cart_id' => $cart_id
            );
            ksort($dataSignature);
            if (!RSA::verifyRSA(json_encode($dataSignature), base64_decode($signature), $site["their_key"])) {
                $dataSing = array();
                $dataSing['status'] = 213;
                if($isHash == 1 && DEVMODE == 1){
                    $dataSing['Signature'] = base64_encode(RSA::generateSignature($dataSignature,$site["private_key"]));
                }
                return System::apiError('Xảy ra lỗi xác thực.', $dataSing);
            }
        }
        /* End Kiểm tra đầu vào nếu truyền  Signature*/
        $cart = array();
        if ($nopay == 1) {
            $cart = Cart::get($cart_id);
        } else {

            $payed = 0;#Thanh toan thanh cong ko
            switch ($agent_site) {
                case 1:#Vi viet

                    #Lay Thong tin thanh toan
                    $vvAgentSite = Url::getParam('vvAgentSite', '');
                    $vvKey = Url::getParam('vvKey', '');
                    $vvData = Url::getParam('vvData', '');

                    if ($vvAgentSite == '' || $vvKey == '' && $vvData != '') {
                        return System::apiError('Thông tin xác thực cổng thanh toán lỗi.', false);
                    }

                    $tripleDesKey = FunctionLib::decryptRSA($vvKey, Payment::VIVIET_MERCHANT_PRIVATE_KEY);

                    $kq = FunctionLib::decrypt($vvData, $tripleDesKey);
                    $arrTemp = explode('|', $kq);
                    if (isset($arrTemp['8']) && $arrTemp['8'] != '') {
                        $return_url = $arrTemp['8'];
                    }
                    if (isset($arrTemp['5'])) {
                        if ($arrTemp['5'] == '0') {
                            $cart_id = $arrTemp['2'];
                            if ($cart_id > 0) {
                                //Thanh toán thành công
                                $cart = Cart::get($cart_id);
                                if ($cart['status'] == IS_ON && $cart['pay_status'] == IS_NO && $cart['site_id'] == $agent_site) {
                                    $payed = 1;
                                }
                            } else {
                                return System::apiError('Không tìm thấy mã đơn hàng', ['return_url' => $return_url, 'payStatus' => 0, 'cartNo' => $cart_id]);
                            }
                        } else {
                            return System::apiError(Payment::viviet_getResponseDescription($arrTemp['5']), ['return_url' => $return_url]);
                        }
                    } else {
                        return System::apiError('Có lỗi thanh toán.', ['return_url' => $return_url, 'payStatus' => 0, 'cartNo' => $cart_id]);
                    }
                    break;
                /*case 2:#VTC
                    if ($cart_id > 0) {
                        //Thanh toán thành công
                        $cart = Cart::get($cart_id);
                        if ($cart['pay_status'] == IS_NO  && $cart['site_id']==$agent_site) {
                            $payed = 1;
                        }
                    } else {
                        return System::apiError('Không tìm thấy mã đơn hàng', ['return_url' => $return_url, 'payStatus' => 0, 'cartNo' => $cart_id]);
                    }
                    break;*/
                /*case 3:#AIR Pay
                    if ($cart_id > 0) {
                        //Thanh toán thành công
                        $cart = Cart::get($cart_id);
                        if ($cart['pay_status'] == IS_NO && $cart['site_id']==$agent_site) {
                            $payed = 1;
                        }
                    } else {
                        return System::apiError('Không tìm thấy mã đơn hàng', ['return_url' => $return_url, 'payStatus' => 0, 'cartNo' => $cart_id]);
                    }
                    break;*/
                case 30:#Viettel
                    #case 50000003:#Viettel TEST
                    $check_sum_vt = Url::getParam('check_sum', '');
                    $billcode = Url::getParam('billcode', '');
                    $cust_msisdn = Url::getParam('cust_msisdn', '');
                    $error_code = Url::getParam('error_code', '');
                    $order_id = Url::getParam('order_id', '');
                    $payment_status = Url::getParam('payment_status', '');
                    $trans_amount = Url::getParam('trans_amount', '');
                    $vt_transaction_id = Url::getParam('vt_transaction_id', '');
                    $merchant_code = Url::getParam('merchant_code', '');
                    $viettepay = new viettepay();

                    if ($error_code == '00') {
                        $string_sum = $billcode . $cust_msisdn . $error_code . $merchant_code . $order_id . $payment_status . $trans_amount . $vt_transaction_id;
                        $check_sum = $viettepay->check_sum($string_sum);
                        if ($check_sum == $check_sum_vt) {
                            $cart = Cart::get($cart_id);
                            if ($cart['status'] == IS_ON && $cart['pay_status'] == IS_NO && $cart['site_id'] == $agent_site) {
                                $payed = 1;
                            }
                        }
                    } else {
                        return System::apiError($viettepay->getError($error_code), ['return_url' => $return_url, 'payStatus' => 0, 'cartNo' => $cart_id]);
                    }

                    break;
                case 8:#Zalo - Onepay

                    //Thanh toan ONEPAY
                    $vpc_TxnResponseCode = Url::getParam('vpc_TxnResponseCode', '');
                    $cart_id = Url::getParamInt('vpc_OrderInfo', 0);
                    $cmd = Url::getParam('cmd', '');

                    if ($vpc_TxnResponseCode != '' && $cart_id > 0) {
                        $onepay = new onepay();
                        //Thanh Toan qua OnePay
                        $verify = $onepay->verifilySecureHas($_GET, $cmd, $mes);

                        $vpc_MerchTxnRef = Url::getParam('vpc_MerchTxnRef', '');
                        if ($verify == 1) {
                            //Thanh toán thành công
                            $payStatus = 1;
                            $cart = Cart::get($cart_id);
                            if ($cart['status'] == IS_ON && $cart['pay_status'] == IS_NO && $cart['site_id'] == $agent_site) {
                                $payed = 1;
                                ZaloProduct::sendConfirm($agent_site, $cart);

                            }
                        } else {
                            return System::apiError($mes, ['return_url' => $return_url, 'payStatus' => 0, 'cartNo' => $cart_id]);
                        }

                    } else {
                        return System::apiError('Không tìm thấy mã đơn hàng', ['return_url' => $return_url, 'payStatus' => 0, 'cartNo' => $cart_id]);
                    }

                    break;
                default:
                    $stringPay = md5($cart_id . $site['private_key'] . $transaction_id . md5('ORTHERPAYMENT'));
                    if ($stringPay == $keyPay) {
                        $cart = Cart::get($cart_id);
                        if ($cart['status'] == IS_ON && $cart['pay_status'] == IS_NO && $payment_status == 2 && $cart['transaction_id'] == $transaction_id && $cart['site_id'] == $site['id']) {
                            $payed = 1;
                        }
                    }
                    break;

            }

            if ($payed == 1) {#Thanh toan thanh cong cập nhật đơn hàng
                Cart::payed($cart_id);
            }
        }

        if (!empty($cart)) {
            if (in_array($site['id'], [1, 3, 8, 30])) {
                $gift_text = '';
                $brand_text = '';
                $detailCart = array();
                if (isset($cart['detail']) && !empty($cart['detail'])) {
                    foreach ($cart['detail'] as $detail) {
                        $gift = isset($cart['gifts'][$detail['gift_id']]) ? $cart['gifts'][$detail['gift_id']] : array();
                        $brand = isset($cart['gifts'][$detail['gift_id']]) ? $cart['gifts'][$detail['gift_id']] : array();
                        $detailCart[] = array(
                            'link' => Bitly::get(LINK_API_URBOX . 'nhan-qua/' . $detail['receive_code'] . '.html'),
                            'title' => $gift['detail'][$detail['gift_detail_id']]['title'],
                            'brand' => $gift['brand']['title']
                        );
                    }
                }

                //$thiep = DB::fetch("SELECT * FROM " . T_GALLERY . " WHERE id=" . $cart['thiep_id']);
                $nguoinhan = Receiver::get($cart['receiver_id']);
                $key = md5($cart['id'] . $cart['customer_id'] . $cart['receiver_id'] . $cart['email'] . $cart['phone'] . 'KIEMTRAMONQUACHUAN');
                $cartReturn = array(
                    'id' => $cart['id'],
                    'cartNo' => $cart['id'],
                    'nguoigui' => $cart['fullname'],
                    'detailCart' => $detailCart,
                    'payStatus' => 1,
                    'message' => $cart['message'],
                    'money_total' => FunctionLib::numberFormat($cart['money_total']),
                    //'thiep' => ImageUrl::getImageURL($thiep['image'], $thiep['created'], 350, 'gallery', 'gallery/'),
                    'nguoinhan' => ($cart['phone'] == $nguoinhan['phone']) ? ' bạn' : ' người nhận',
                    'dienthoainguoinhan' => ($nguoinhan['phone'] != '' && $nguoinhan['phone'] != '0999999999') ? $nguoinhan['phone'] : '',
                    'thoigiangui' => $cart['send_time'] > 0 ? FunctionLib::dateFormat($cart['send_time'], 'd/m/Y') : 'Gửi ngay',
                    'link' => Bitly::get(LINK_API_URBOX . 'kiem-tra-qua/' . $key . '.html?cart_no=' . $cart['id'])
                );
                return System::apiSuccess(array('cart' => $cartReturn, 'return_url' => $return_url));

            } else {
                if ($payed == 1) {
                    #Return
                    $cartLink = array();
                    $cartCodeLink = array();
                    if (isset($cart['detail']) && $cart['detail']) {
                        $giftSusID = array();
                        $giftSusArr = array();

                        $giftCodeArr = array();
                        foreach ($cart['detail'] as $detail) {
                            GiftCode::assignCode($detail['id']);
                            $giftSusID[$detail['gift_id']] = $detail['gift_id'];
                            $giftDetailSusID[$detail['gift_detail_id']] = $detail['gift_detail_id'];
                        }
                        $cart = Cart::get($cart_id);
                        if (!empty($giftSusID)) {
                            $giftSusArr = DB::fetch_all("SELECT id,type,brand_id,showbarcode,isPhysical,pointu_id FROM " . T_GIFT . " WHERE id IN (" . implode(',', $giftSusID) . ")");
                        }
                        $regiftCodeArr = DB::query("SELECT id,cart_detail_id,code,status,expired,pin FROM " . T_GIFT_CODE . " WHERE cart_detail_id IN (" . implode(',', array_keys($cart['detail'])) . ")");
                        if ($regiftCodeArr) {
                            while ($rowe = @mysql_fetch_assoc($regiftCodeArr)) {
                                $giftCodeArr[$rowe['cart_detail_id']] = $rowe;
                            }
                        }
                        foreach ($cart['detail'] as $detail) {
                            $estimateDelivery = '';
                            $code = '';
                            $pin = '';
                            $expired = '';
                            $gift = isset($giftSusArr[$detail['gift_id']]) ? $giftSusArr[$detail['gift_id']] : (DB::fetch("SELECT * FROM " . T_GIFT . " WHERE id=" . $detail['gift_id']));
                            $gift_code = isset($giftCodeArr[$detail['id']]) ? $giftCodeArr[$detail['id']] : (DB::fetch("SELECT * FROM " . T_GIFT_CODE . " WHERE cart_detail_id=" . $detail['id']));
                            if ($gift_code) {
                                $code = System::decrypt($gift_code['code']);
                                $pin = $gift_code['pin'];
                                $link = LINK_API_URBOX . 'nhan-qua/' . $detail['receive_code'] . '.html';
                                if ($gift['status'] == 3) {
                                    $link = LINK_API_URBOX . 'nhan-qua-doi-tac/' . $detail['receive_code'] . '.html';
                                }
                                $expired = $gift_code['expired'] == 0 ? 'Vô hạn' : FunctionLib::dateFormat($gift_code['expired'], 'd/m/Y');
                                if ($gift['type'] == 4) {
                                    $link = DOMAIN_LOYAL . 'card/' . System::decrypt($gift_code['code']);
                                    $code = DB::fetch("SELECT number FROM " . T_CARD . " WHERE token='" . $gift_code['code'] . "'", 'number', '');
                                }
                                if ($gift['type'] == 7) {
                                    $link = DOMAIN_LOYAL . 'vna/miles/' . System::decrypt($gift_code['code']);
                                }
                                if ($gift['type'] == 12) {
                                    $link = DOMAIN_LOYAL . 'receiver/' . $detail['receive_code'];
                                }

                                if ($gift['isPhysical'] == 2 || $gift['type'] == 9) {
                                    $code = '';
                                    $expired = '';
                                    $estimateDelivery = 'Dự kiến giao hàng từ 3 - 5 ngày làm việc.';
                                }
                            }
                            $link = $link;
                            $code_image = '';

                            if ($gift['showbarcode'] == 2) {
                                if ($gift['brand_id'] == 305) {
                                    $code_image = 'https://barcode.tec-it.com/barcode.ashx?data=' . urlencode($code) . '&code=EAN13&multiplebarcodes=false&translate-esc=false&unit=Fit&dpi=96&imagetype=Gif&rotation=0&color=%23000000&bgcolor=%23ffffff&qunit=Mm&quiet=0';
                                } else {
                                    $code_image = 'https://barcode.tec-it.com/barcode.ashx?data=' . urlencode($code) . '&code=Code128&multiplebarcodes=false&translate-esc=false&unit=Fit&dpi=96&imagetype=Gif&rotation=0&color=%23000000&bgcolor=%23ffffff&qunit=Mm&quiet=0&dmsize=Default';
                                }
                            } else {
                                $code_image = 'https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=' . urlencode($code);
                            }

                            $cartLink[] = $link;

                            $cartCodeLink[] = array(
                                'cart_detail_id' => $detail['id'],
                                'code_display' => ($gift['isPhysical']==2 || $gift['type'] == 9)?'Vật lý':(($gift['showbarcode']==1)?'QR code':($gift['showbarcode']==2?'Barcode 128':'Text')),
                                'code_display_type' => ($gift['isPhysical']==2 || $gift['type'] == 9)?4:(($gift['showbarcode']==1)?1:($gift['showbarcode']==2?2:3)),
                                'link' => $link,
                                'code' => $code,
                                'pin' => $pin,
                                'priceId' => $detail['gift_detail_id'],
                                'token' => $detail['receive_code'],
                                'expired' => $expired,
                                'code_image' => $gift['isPhysical'] == 1 ? $code_image : '',
                                'estimateDelivery' => t($estimateDelivery),
                            );
                        }
                    }
                    $key = md5($cart['id'] . $cart['customer_id'] . $cart['receiver_id'] . $cart['email'] . $cart['phone'] . 'KIEMTRAMONQUACHUAN');
                    $linkCart = LINK_API_URBOX . 'cart/' . $key . '.html?cart_no=' . $cart['id'];

                    return System::apiSuccess(
                        array(
                            'pay' => IS_YES,
                            'transaction_id' => $transaction_id,
                            'linkCart' => $linkCart,
                            'cart' =>
                                array(
                                    'id' => $cart['id'],
                                    'cartNo' => $cart['id'],
                                    'money_total' => $cart['money_total'],
                                    'money_ship' => $cart['money_ship'],
                                    'link_gift' => $cartLink,
                                    'code_link_gift' => $cartCodeLink
                                )
                        )
                    );
                } else {
                    if ($payment_status == 3 && $cart['status'] == IS_ON && $cart['pay_status'] == IS_NO && $cart['transaction_id'] == $transaction_id && $cart['site_id'] == $site['id']) {
                        DB::update(T_CART, array('status' => -1), 'id=' . $cart['id']);
                        DB::update(T_CART_DETAIL, array('status' => -1), 'cart_id=' . $cart['id']);
                        return System::apiSuccess(
                            array(
                                'pay' => 3,
                                'transaction_id' => $transaction_id,
                            )
                        );
                    }
                }
            }
        }

        return System::apiError('Có lỗi xảy ra khi thanh toán', ['return_url' => $return_url, 'payStatus' => 0, 'cartNo' => $cart_id, 'status' => 103]);

    }

    function cartSubmit()
    {

        #Kiem tra access_urbox
        $access_urbox = Url::getParam('access_urbox', '');
        $agent_site = Url::getParamInt('agent_site', 0);
        $isHash = Url::getParamInt('isHash', 0);
        $app_secret = Url::getParam('app_secret', '');
        $app_id = Url::getParamInt('app_id', 0);
        $transaction_id = Url::getParam('transaction_id', '');

        if ($app_secret != '') {
            $access_urbox = $app_secret;
        }
        if ($app_id != '') {
            $agent_site = $app_id;
        }

        if (!Site::check_access_urbox($agent_site, $access_urbox,$site)) {
            return System::apiError('Access Urbox không đúng.', ['status'=>211]);
        }

        #Lay thong tin nguoi dang nhap
        $emailnguoigui = trim(Url::getParam('emailnguoigui', ''));
        $nguoigui = trim(Url::getParam('nguoigui', ''));
        $phonenguoigui = trim(Url::getParam('phonenguoigui', ''));

        $site_user_id = Url::getParam('site_user_id', '');

        #Lay thong tin don hang
        $ttmessage = trim(Url::getParam('ttmessage', ''));
        $ttemail = trim(Url::getParam('ttemail', ''));
        $ttphone = trim(Url::getParam('ttphone', ''));
        $ttfullname = trim(Url::getParam('ttfullname', ''));
        $ttindentify = trim(Url::getParam('ttindentify', ''));
        $ttcoupon = trim(Url::getParam('ttcoupon', ''));
        $ttaddress = trim(Url::getParam('ttaddress', ''));
        $ttDateSend = trim(Url::getParam('ttDateSend', ''));
        $external_data = (Url::getParam('external_data', ''));
        $ttDateSendmkTime = Url::getParamInt('ttDateSend', 0);
        $ip = Url::getParam('ip', '');
        $dataBuy = trim(html_entity_decode(Url::getParam('dataBuy', '')));
        $data = trim(html_entity_decode(Url::getParam('data', '')));

        $thiep_id = Url::getParamInt('thiep_id', 0);

        $arrCart = json_decode(StringLib::post_db_parse_html($dataBuy), true);
        $send_time = 0;
        if ($ttDateSend != '') {
            $arrTime = array(0 => 8, 1 => 30);
            $arrDate = explode('/', $ttDateSend);
            if (!empty($arrDate) && !empty($arrTime)) {
                $send_time = mktime((int)$arrTime[0], (int)$arrTime[1], 0, (int)$arrDate[1], (int)$arrDate[0], (int)$arrDate[2]);
            }
        }
        if ($ttDateSendmkTime > 0) {
            $send_time = $ttDateSendmkTime;
        }
        /* Kiểm tra đầu vào nếu truyền  Signature*/
        $signature = Url::getParam('Signature', '');
        if ($site["their_key"] != '') {
            $dataSignature = array(
                'app_secret' => $access_urbox,
                'app_id' => $agent_site,
                'transaction_id' => $transaction_id,
                'dataBuy' => $arrCart
            );
            ksort($dataSignature);
            if (!RSA::verifyRSA(json_encode($dataSignature), base64_decode($signature), $site["their_key"])) {
                $dataSing = array();
                $dataSing['status'] = 213;
                if($isHash == 1 && DEVMODE == 1){
                    $dataSing['Signature'] = base64_encode(RSA::generateSignature($dataSignature,$site["private_key"]));
                }
                return System::apiError('Xảy ra lỗi xác thực.', $dataSing);
            }
        }
        /* End Kiểm tra đầu vào nếu truyền  Signature*/
        if (!in_array($site['id'], [1, 3, 8, 30])) {
            #Kiem tra $transaction_id neu co
            #Se xoa cac don hang yeu cau xoa va tao lai
            if ($transaction_id != '') {
                $check_transaction_site = DB::fetch("SELECT id,pay_status,customer_id,receiver_id,email,phone,money_total FROM " . T_CART . " WHERE status=" . IS_ON . " AND site_id=" . $site['id'] . " AND transaction_id='" . $transaction_id . "'");
                if ($check_transaction_site) {
                    return System::apiError('Mã giao dịch này đã tồn tại.',['status'=>401]);
                }
            }else{
                return System::apiError('Không tìm thấy Mã Giao Dịch (transaction_id).',['status'=>403]);
            }
        }

        $ttemail = Customer::fixEmail($ttemail);
        $isPhysical = false;
        $arrGiftId = array();
        if (!empty($arrCart)) {
            $giftDetailID = array();
            foreach ($arrCart as $key => $item) {
                if ((int) $item['quantity'] <= 0) {
                    return System::apiError('Số lượng phải lớn hơn 0', ['status'=>404]);
                } else {
                    if (FunctionLib::check_number($item['priceId'])) {
                        $arrCartDetail[$item['priceId']] = array('quantity' => $item['quantity']);
                        $giftDetailID[$item['priceId']] = (int)$item['priceId'];
                        if (!isset($item['id']) || (int)$item['id'] == 0) {
                            /*$giftID = DB::fetch("SELECT gift_id from " . T_GIFT_DETAIL . " WHERE id=" . ((int)$item['priceId']) . " LIMIT  1");
                            if(!empty($giftID)){
                                $arrGiftId[$giftID['gift_id']] = $giftID['gift_id'];
                                $arrCart[$key]['id'] = $giftID['gift_id'];
                            }*/
                        } else {
                            if ($item['id'] > 0) {
                                $arrGiftId[$item['id']] = $item['id'];
                            }
                        }
                    } else {
                        return System::apiError('Không tìm thấy quà tặng', ['status'=>224]);
                    }
                }
                $arrCart[$key]['quantity']  =  $item['quantity'];
                $arrCart[$key]['priceId']  =  $item['priceId'];
            }
            if(!empty($giftDetailID)){
                $giftIDArr = DB::query("SELECT gift_id,id,code_quantity,title from " . T_GIFT_DETAIL . " WHERE id IN (" . implode(',',$giftDetailID) .')');
                if($giftIDArr) {
                    $giftexpired = array();
                    while ($row = @mysql_fetch_assoc($giftIDArr)) {
                        $arrGiftId[$row['gift_id']] = $row['gift_id'];
                        if ($row['code_quantity'] <= 0) {
                            $giftexpired[] = array('id' => $row['id'], 'title' => $row['title']);
                        }
                        foreach ($arrCart as $key => $item) {
                            if ($item['priceId'] == $row['id']) {
                                $arrCart[$key]['id'] = $row['gift_id'];
                            }
                        }
                    }
                    if (!empty($giftexpired)) {
                        return System::apiError('1 trong số quà tặng bạn đặt mua đã hết hạn, bạn hãy chọn lại quà khác', array('data' => $giftexpired, 'status' => 223));
                    }
                    if (count($giftDetailID) != count($arrGiftId)) {
                        return System::apiError('Không tìm thấy quà tặng', ['status' => 224]);
                    }
                }
            }
            if (!empty($arrGiftId)) {
                $gift_check = array();
                $regift_check = DB::query("SELECT id,isPhysical,type FROM " . T_GIFT . " WHERE status IN(1,2) AND id IN(" . implode(',', $arrGiftId) . ")");
                if ($regift_check) {
                    while ($rowGC = mysql_fetch_array($regift_check)) {
                        $gift_check[$rowGC['id']] = $rowGC;
                        if ($rowGC['isPhysical'] == 2 || $rowGC['type'] == 9) {
                            $isPhysical = true;
                        }
                    }
                }
                if (count($gift_check) != count($arrGiftId)) {
                    return System::apiError('1 trong số quà tặng bạn đặt mua đã hết hạn, bạn hãy chọn lại quà khác',['status'=>223]);
                }
            } else {
                return System::apiError('Không tìm thấy quà tặng', ['status'=>224]);
            }
        } else {
            return System::apiError('Bạn vui lòng chọn quà muốn tặng trước.',['status'=>405]);
        }

        if ($ttemail != '' && !FunctionLib::is_valid_email($ttemail)) {
            return System::apiError('Email không đúng định dạng', ['status'=>304]);
        }

        if ($ttcoupon != '' && !Coupon::isValid($ttcoupon, $arrGiftId)) {
            return System::apiError('Mã khuyến mại không đúng', ['status'=>406]);
        }

        /*#Xem có dang nhap ơ dau ko rồi lấy thông tin nhập update vào customer
        $access_token = trim(Url::getParam('access_token', ''));
        $customer_id = 0;
        Customer::isValidToken($access_token, $customer_id);
        if ($customer_id == 0) {
            $access_token_arr = explode('_', $access_token);
            if (!empty($access_token_arr)) {
                if (isset($access_token_arr[0]) && isset($access_token_arr[1]) && $access_token_arr[0] == md5($access_token_arr[1])) {
                    $site_user_id = $access_token_arr[1];
                    $checkEmailExit = DB::fetch('SELECT * FROM ' . T_CUSTOMER . " WHERE email='" . $emailnguoigui . "'");
                    if (!empty($checkEmailExit) && $emailnguoigui != '<EMAIL>') {
                        $customer_id = $checkEmailExit['id'];
                        if ($customer_id > 0) {
                            DB::update(T_APP_CUSTOMER, array('customer_id' => $customer_id), 'user_id=' . $access_token_arr[1] . ' AND site_id=' . $agent_site);
                        }
                    } else {
                        $checkPhoneExit = DB::fetch('SELECT * FROM ' . T_CUSTOMER . " WHERE phone='" . $phonenguoigui . "'");
                        if (!empty($checkPhoneExit) && $phonenguoigui != '0999999999') {
                            $customer_id = $checkPhoneExit['id'];
                            if ($customer_id > 0) {
                                DB::update(T_APP_CUSTOMER, array('customer_id' => $customer_id), 'user_id=' . $access_token_arr[1] . ' AND site_id=' . $agent_site);
                            }
                        }

                    }

                }
            }
        }

        #End*/
        if ($ttphone != '' && !FunctionLib::is_phone($ttphone)) {
            return System::apiError('Số điện thoại không đúng định dạng',['status'=>610]);
        }
        $address_id = 0;
        #Thông tin lấy hàng
        $shipping_info_available = Url::getParamInt('shipping_info_available',0);
        $city_id = Url::getParamInt('city_id',0);
        $district_id = Url::getParamInt('district_id',0);
        $ward_id = Url::getParamInt('ward_id',0);
        $delivery_note = Url::getParam('delivery_note','');
        $address_id = Url::getParam('address_id',0);
        if($isPhysical == true && $shipping_info_available == 2 && $address_id==0){
            #Can Validate Các trường
            if ($ttemail != '' && !FunctionLib::is_valid_email($ttemail)) {
                return System::apiError('Email không đúng định dạng', ['status'=>304]);
            }
            if ($city_id == 0) {
                return System::apiError('Không tìm thấy mã tỉnh thành phố',['status'=>602]);
            }
            $city = DB::fetch("SELECT id FROM ".T_PROVINCE." WHERE id=".$city_id);
            if(empty($city)){
                return System::apiError('Tỉnh thành bạn nhập không tồn tại',['status'=>603]);
            }
            if ($district_id == 0) {
                return System::apiError('Không tìm thấy mã quận huyện',['status'=>604]);
            }
            $district = DB::fetch("SELECT id,city_id FROM " . T_DISTRICT . " WHERE id=" . $district_id);
            if (empty($district)) {
                return System::apiError('Quận huyện bạn nhập không tồn tại',['status'=>605]);
            }
            if($district['city_id'] != $city_id){
                return System::apiError('Quận huyện không thuộc thành phố bạn chọn',['status'=>606]);
            }
            if ($ward_id == 0) {
                return System::apiError('Không tìm thấy mã phường xã',['status'=>607]);
            }
            $ward = DB::fetch("SELECT id,district_id FROM " . T_WARD . " WHERE id=" . $ward_id);
            if (empty($ward)) {
                return System::apiError('Phường xã bạn nhập không tồn tại',['status'=>608]);
            }
            if($ward['district_id'] != $district_id){
                return System::apiError('Xã phường không thuộc tỉnh thành bạn chọn',['status'=>609]);
            }
            if ($ttaddress == '') {
                return System::apiError('Không tìm thấy địa chỉ',['status'=>610]);
            }
            if ($ttphone == '' || !FunctionLib::is_phone($ttphone)) {
                return System::apiError('Không tìm thấy số điện thoại',['status'=>610]);
            }
        }
        if($shipping_info_available == 2 && $address_id==0){
            if($ttemail!='' || $ttphone!=''){
                $receiver = Receiver::add(array(
                    'first_name' => $ttfullname,
                    'fullname' => $ttfullname,
                    'email' => $ttemail,
                    'phone' => $ttphone,
                ));
                if($receiver){
                    $entity = [
                        "fullname" => $ttfullname,
                        "phone" => System::encrypt($ttphone),
                        "email" => System::encrypt($ttemail),
                        "city_id" => $city_id,
                        "ward_id" => $ward_id,
                        "number" => $ttaddress,
                        "district_id" => $district_id,
                        "receiver_id" => $receiver['id'],
                        "status" => 2
                    ];
                    $address_id = Cart::saveAddress($entity);
                }
            }
        }

        #END
        if (!empty($arrCartDetail)) {
            $detailGifts = Gift::getDetailByID(array_keys($arrCartDetail));

            foreach ($detailGifts as $k => $quan) {
                if (in_array($quan['gift_id'], $arrGiftId)) {
                    if ($quan['code_quantity'] < $arrCartDetail[$quan['id']]['quantity']) {
                        $gift = DB::fetch("SELECT id FROM ".T_GIFT." WHERE id=".$quan['gift_id']);
                        return System::apiError('Số lượng sản phẩm không đủ', array('gift' => $gift,'status'=>407));
                    }
                } else {
                    return System::apiError('1 trong số quà tặng bạn đặt mua đã hết hạn, bạn hãy chọn lại quà khác.', ['status'=>223]);
                }
            }
        }
        #Kiem tra qua ko co trong Bo Qua khong cho mua
        if($site['gift_id']>0 && !empty($arrCartDetail)){
            $giftSet = Gift::giftSetbyGiftID($site['gift_id']);
            $errGiftSet = 0;
            if(!empty($giftSet)){
                foreach ($arrCartDetail as $k => $giftDe){
                    if(!isset($giftSet[$k])){
                        $errGiftSet = 1;
                    }
                }
            }else{
                $errGiftSet = 1;
            }
            if($errGiftSet == 1){
                $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>226,'app_id' => $agent_site);
                return System::apiError(t(isset(System::$data['status'][226]) ? System::$data['status'][226]['title'] : 'Quà tặng không nằm trong chương trình'), ['status'=>226],$saveCartErr);
            }
        }
        #END
        $cart = array(
            'from' => array('name' => $nguoigui, 'phone' => $phonenguoigui, 'email' => $emailnguoigui, 'message' => $ttmessage, 'thiep_id' => $thiep_id),
            'to' => array('name' => $ttfullname, 'phone' => $ttphone, 'email' => $ttemail),
            'items' => $arrCart,
            'coupon' => $ttcoupon,
            'address' => $ttaddress,
            'site_id' => $agent_site,
            'site_user_id' => $site_user_id,
            'send_time' => $send_time,
            'delivery_note' => $delivery_note,
            'address_id' => $address_id,
            'ip' => $ip
        );
        $arrCartFinal = Cart::createOrder($cart, $msg);
        if ($msg == '') {
            $msg = 'Hệ thống hiện tại không tạo được đơn hàng.';
        }
        if (!empty($arrCartFinal)) {
            #Khi tao dc don hang thi se luu transaction_id
            if ($transaction_id != '') {
                $updateCart['transaction_id'] = $transaction_id;
            }
            if (!empty($updateCart)) {
                DB::update(T_CART, $updateCart, 'id=' . $arrCartFinal['id']);
            }
            #END
            if ($arrCartFinal['money_total'] <= 0) {
                #Cap nhat don hang thanh cong
                Cart::payed($arrCartFinal['id']);
                $cart = Cart::get($arrCartFinal['id']);
                switch ($agent_site) {
                    case 8:#Zalo
                        ZaloProduct::sendConfirm($agent_site, $cart);
                        break;
                    case LoyaltyCampaign::APP_PVCB:
                    case LoyaltyCampaign::APP_BAMBOO:
                    case LoyaltyCampaign::APP_SABRE:
                    case LoyaltyCampaign::APP_HANWHA:
                    case LoyaltyCampaign::APP_MBAL:
                    case LoyaltyCampaign::APP_VCB_REWARD:
                    case LoyaltyCampaign::APP_VCB_VISA:
                    case LoyaltyCampaign::APP_VISA_DEBIT:
                        #App Loyal thì insert thêm
                        $campaign_id = Url::getParamInt('campaign_id', 0);
                        $point = Url::getParamInt('point', 0);
                        $exchange_rate = Url::getParamInt('exchange_rate', 0);
                        if($campaign_id>0 && $point>=0 && $exchange_rate>0){
                            DB::insert(T_LOYAL_ORDER,array(
                                'campaign_id' => $campaign_id,
                                'site_user_id' => $site_user_id,
                                'point' => $point,
                                'exchange_rate' => $exchange_rate,
                                'money' => $arrCartFinal['money_total'],
                                'cart_id' => $arrCartFinal['id'],
                                'app_id' => $app_id,
                                'pay_status' => 1,
                                'created' => TIME_NOW
                            ));
                        }
                        break;
                }
                if (isset($cart['detail']) && $cart['detail']) {
                    foreach ($cart['detail'] as $detail) {
                        #insert thêm thông tin
                        if(!is_string($external_data)) $external_data = json_encode($external_data);
                        Cart::addCartDetailInfo($detail['id'],$ttfullname,$ttemail,$ttphone,$ttindentify, $data);
                        #end
                    }
                }
                #Thanh toan thanh cong
                #return System::apiSuccess(array('pay' => 1, 'cart' => $cart));
                return System::apiSuccess(array('pay' => 1, 'access_urbox' => $access_urbox, 'agent_site' => $agent_site, 'cart' => array('id' => $cart['id'], 'cartNo' => $cart['id'], 'money_total' => $cart['money_total'])));
            } else {
                $payUrl = '';
                switch ($agent_site) {
                    case 1:#Vi viet
                        #Lay Thong tin thanh toan
                        $vvAgentSite = Url::getParam('vvAgentSite', '');
                        $vvKey = Url::getParam('vvKey', '');
                        $vvData = Url::getParam('vvData', '');
                        if ($vvAgentSite == '' || $vvKey == '' && $vvData != '') {
                            return System::apiError('Thông tin xác thực cổng thanh toán lỗi.', false);
                        }

                        $payUrl = Payment::getUrl(array(
                            'by' => 'viviet',
                            'money' => $arrCartFinal['money_total'],
                            'cart_id' => $arrCartFinal['id'],
                            'vvAgentSite' => $vvAgentSite,
                            'vvKey' => $vvKey,
                            'vvData' => $vvData,

                            'access_urbox' => $access_urbox,
                            'agent_site' => $agent_site,
                        ));
                        break;

                    case 3:#AIR PAY
                        $payUrl = Payment::getUrl(array(
                            'by' => 'airpay',
                            'money' => $arrCartFinal['money_total'],
                            'cart_id' => $arrCartFinal['id'],

                            'access_urbox' => $access_urbox,
                            'agent_site' => $agent_site,
                        ));
                        break;
                    case 30:#Viettel
                        #case 50000003:#Viettel TEST
                        $payUrl = Payment::getUrl(array(
                            'by' => 'viettelpay',
                            'money' => $arrCartFinal['money_total'],
                            'cart_id' => $arrCartFinal['id'],

                            'access_urbox' => $access_urbox,
                            'agent_site' => $agent_site,
                        ));
                        break;
                    case 8:
                        #Zalo- Dùng Cồng ONEPAY
                        #Loai thanh toan
                        $onepay_opt = Url::getParamInt('onepay', 2);

                        $onepay = new onepay();

                        if ($onepay_opt == 2) {
                            $onepay->vpc_ReturnURL = LINK_INTEGRATE . 'success.html?agent_site=' . $agent_site . '&cart_id=' . $arrCartFinal['id'] . '&access_urbox=' . $access_urbox;
                        } else {
                            $onepay->vpc_ReturnURL = LINK_INTEGRATE . 'success.html?agent_site=' . $agent_site . '&cart_id=' . $arrCartFinal['id'] . '&access_urbox=' . $access_urbox . '&cmd=onepay';
                        }
                        $payUrl = $onepay->buildSercureURL($onepay_opt, $arrCartFinal['id'], $arrCartFinal['id'], $arrCartFinal['money_total']);

                        break;
                    case LoyaltyCampaign::APP_PVCB:
                    case LoyaltyCampaign::APP_BAMBOO:
                    case LoyaltyCampaign::APP_SABRE:
                    case LoyaltyCampaign::APP_HANWHA:
                    case LoyaltyCampaign::APP_MBAL:
                    case LoyaltyCampaign::APP_VCB_REWARD:
                    case LoyaltyCampaign::APP_VCB_VISA:
                    case LoyaltyCampaign::APP_VISA_DEBIT:
                        #App Loyal thì insert thêm
                        $campaign_id = Url::getParamInt('campaign_id', 0);
                        $point = Url::getParamInt('point', 0);
                        $exchange_rate = Url::getParamInt('exchange_rate', 0);
                        if($campaign_id>0 && $point>=0 && $exchange_rate>0){
                            DB::insert(T_LOYAL_ORDER,array(
                                'campaign_id' => $campaign_id,
                                'site_user_id' => $site_user_id,
                                'point' => $point,
                                'exchange_rate' => $exchange_rate,
                                'money' => $arrCartFinal['money_total'],
                                'cart_id' => $arrCartFinal['id'],
                                'app_id' => $app_id,
                                'pay_status' => 1,
                                'created' => TIME_NOW
                            ));
                        }
                        break;
                }

                $cart = Cart::get($arrCartFinal['id']);
                if (isset($cart['detail']) && $cart['detail']) {
                    foreach ($cart['detail'] as $detail) {
                        #insert thêm thông tin
                        if(!is_string($external_data)) $external_data = json_encode($external_data);
                        Cart::addCartDetailInfo($detail['id'],$ttfullname,$ttemail,$ttphone,$ttindentify,$data);
                        #end
                    }
                }
                if( in_array($site['id'],[1,3,8,30])){
                    return System::apiSuccess(array('pay' => 2, 'payUrl' => $payUrl, 'access_urbox' => $access_urbox, 'agent_site' => $agent_site, 'cart' => array('id' => $cart['id'], 'cartNo' => $cart['id'], 'money_total' => $cart['money_total'])));
                } else {
                    return System::apiSuccess(
                        array(
                            'pay' => 2,
                            'cart' => array(
                                'id' => $cart['id'],
                                'cartNo' => $cart['id'],
                                'transaction_id' => $transaction_id,
                                'keyPay' => md5($cart['id'] . $site['private_key'] . $transaction_id . md5('ORTHERPAYMENT')),
                                'money_total' => $cart['money_total']
                            )
                        )
                    );
                }

            }

        } else {
            return System::apiError($msg,['status'=>408]);
        }


    }

    function buy()
    {
        echo 'Hệ thống hiện không hỗ trợ mua bằng phương thức này';
        die;

    }

    function pay()
    {
        echo 'Hệ thống hiện không hỗ trợ mua bằng phương thức này';
        die;

    }

    function cartPayVoucher()
    {
        $site_user_id = Url::getParam('site_user_id', '');
        $transaction_id = Url::getParam('transaction_id', '');
        $address_id = Url::getParamInt('address_id', 0);
        $urcard_id = Url::getParamInt('urcard_id', 0);
        $wallet_id = Url::getParamInt('wallet_id', 0);
        $channel = Url::getParamInt('channel', 1);
        $metadata = Url::getParam('metadata', '');
        #Kiem tra access_urbox
        $access_urbox = Url::getParam('access_urbox', '');
        $agent_site = Url::getParam('agent_site', '');

        $app_secret = Url::getParam('app_secret', '');
        $app_id = Url::getParam('app_id', '');

        $shorten = Url::getParamInt('shorten', 0);
        $callback_transaction = Url::getParamInt('callback_transaction', 0);

        if ($app_secret != '') {
            $access_urbox = $app_secret;
        }
        if ($app_id != '') {
            $agent_site = $app_id;
        }
        if ($agent_site == '' || $access_urbox == '') {
            $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>210,'app_id' => $agent_site);
            return System::apiError((isset(System::$data['status'][210]) ? System::$data['status'][210]['title'] : 'Không tìm app_id hoặc app_secret'), ['status'=>210],$saveCartErr);
        }
        $site = array();
        if (!Site::check_access_urbox($agent_site, $access_urbox,$site)) {
            $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>211,'app_id' => $agent_site);
            return System::apiError((isset(System::$data['status'][211]) ? System::$data['status'][211]['title'] : 'Access Urbox không đúng.'), ['status'=>211],$saveCartErr);
        }

        $agent_site = $site['id'];
        $app_id = $site['id'];
        #Ngày 10-1-2024 kiểm tra hệ thống POM với APP+CampaignCODE
        $campaign_code = Url::getParam('campaign_code', '');
        $subPOCode = Url::getParam('subPOCode', '');
        $partnerPOCode = Url::getParam('partnerPOCode', '');
        $moneyPom = 0;
        if($site['is_pom'] != 1){#Nếu không chạy POM
            if($site['is_pom'] == 3){#Loại APP chạy POM 1-1
                $campaign_code = $site['campaign_code'];
            }
            if($campaign_code ==''){
                #Mã lỗi khi loại APP cần truyền POM nhưng lại không truyền POM
                $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>0,'app_id' => $agent_site);
                return System::apiError((isset(System::$data['status'][307]) ? System::$data['status'][307]['title'] : 'Request thiếu thông tin CampaignCode.'), ['status'=>307],$saveCartErr);
            }
            if(!Urcard::checkPomCampaignCode($app_id,$campaign_code,$subPOCode,$partnerPOCode,$moneyPom)){
                #Mã lỗi khi campaign_code và app_id không khớp
                $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>0,'app_id' => $agent_site);
                return System::apiError((isset(System::$data['status'][308]) ? System::$data['status'][308]['title'] : 'Mã chương trình không thuộc app_id.'), ['status'=>308],$saveCartErr);
            }
            if($moneyPom <= 0){
                $message_err = t(isset(System::$data['status'][309]) ? System::$data['status'][309]['title'] : 'Ngân sách chương trình không đủ để thực hiện giao dịch');
                $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>309,'app_id' => $agent_site);
                return System::apiError($message_err, ['status'=>309],$saveCartErr);
            }
        }
        #END
        if($site['id'] == 124){
            if(!System::checkVTP(1)){
                return false;
            }
        }
        $nameOneTranSite = $transaction_id.'_'.$site['id'];
        if(!FunctionLib::redisFlag($nameOneTranSite,1,5)){
            $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>415,'app_id' => $agent_site);
            return System::apiError(t(isset(System::$data['status'][415]) ? System::$data['status'][415]['title'] : 'Đơn hàng chưa được xử lý. Vui lòng thực hiện lại giao dịch này.'), ['status'=>415],$saveCartErr);
        }
        #Ngày 2-3-2022 AIA cần 1 tham số riêng để Xác thực
        if($site['id'] == 500000211){
            $ShVmYq3t = Url::getParam('ShVmYq3t', '');
            $aia_check = Url::getParam('aia_check', '');
            if($ShVmYq3t !='ShVmYq3t' && $aia_check!='70ecfbc1383c4693b182c51bdfe78f06'){
                $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>213,'app_id' => $agent_site);
                return System::apiError(t(isset(System::$data['status'][213]) ? System::$data['status'][213]['title'] : 'Xảy ra lỗi xác thực.'), ['status'=>213],$saveCartErr);
            }
        }
        #END
        $shipping_info_available = Url::getParamInt('shipping_info_available',0);
        #Nhan Yeu cau ben Doi tac ep trường đã có EMail thông báo
        if($site['id'] == 226){
            $shipping_info_available = 2;
        }
        #END
        #Thông tin cho TOPUP
        $firstname = Url::getParam('firstname', '');
        $lastname = Url::getParam('lastname', '');
        $member_id = Url::getParam('member_id', '');
        #END
        #Ngon Ngu
        $lang = Url::getParam('lang', 'vi');
        Language::$activeLang = $lang;
        Language::loadWordsFromLang(Language::$activeLang);
        #End
        #TRuyền thêm mã PIN khi khách đặt mua BALACE
        $pin = Url::getParam('pin', '');
        if($pin !=''){
            if( strlen($pin) != 6){
                $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>409,'app_id' => $agent_site);
                return System::apiError( 'Độ dài Pin phải là 6 ký tự.', ['status'=>409],$saveCartErr);
            }
            if( !is_numeric($pin) ){
                $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>409,'app_id' => $agent_site);
                return System::apiError( 'Pin phải là số.', ['status'=>409],$saveCartErr);
            }
        }
        #End

        /*$site = DB::fetch("SELECT * FROM " . T_APP . " WHERE id=" . $agent_site);*/

        /*$customer_id = 0;
        if ($site) {
            $customer_id = $site['customer_id'];
        }*/

        #Ngay 06-11-2019 Them yeu cau OTP truoc khi tru tien
        $verity = array();
        if ($site['checkOtp'] == IS_YES) {
            $otp = Url::getParam('otp', '');
            $verity = DB::fetch("SELECT * FROM ".T_APP_OTP." WHERE app_id=".$site['id']." AND transaction_id='".$transaction_id."' AND otp='".$otp."' AND process=".IS_NO." AND status=".IS_YES."  AND  otp_time>=".TIME_NOW);
            if(empty($verity)){
                $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>212,'app_id' => $agent_site);
                return System::apiError(t(isset(System::$data['status'][212]) ? System::$data['status'][212]['title'] : 'Thông tin xác thực không chính xác.'), ['status'=>212],$saveCartErr);
            }
        }
        $isSendSms = Url::getParamInt('isSendSms', 0);
        #Ngay 8/9/2019
        #Cho Site ViettelCSKH gui SMS
        if ($site['id'] == 32 || $site['id'] == 36) {
            $isSendSms = 1;
        }
        #END
        #Ngày 6-5-2022
        #Game MBAL thêm loại quà đặc biệt sẽ truyền code và hạn sử dụng sang
        $mb_code = Url::getParam('mb_code','');#Code: Kiểu String
        $mb_expired = Url::getParamInt('mb_expired',0);#Thời gian hết hạn: Kiểu số dạng TimeStamp
        #END
        #Ngày 19-1-2024
        #Nhận thêm tham số IS_COD để đánh dấu đơn hàng cần thu tiền khi giao hàng
        $is_cod = Url::getParamInt('is_cod',0);#=1 đánh dấu COD
        if($is_cod!=1){
            $is_cod = 0;
        }
        #END
        #Lay thong tin don hang

        $ttmessage = trim(Url::getParam('ttmessage', ''));
        $ttemail = trim(Url::getParam('ttemail', ''));
        $ttphone = trim(Url::getParam('ttphone', ''));
        $ttfullname = trim(Url::getParam('ttfullname', ''));
        $ttindentify = trim(Url::getParam('ttindentify', ''));
        $ttcoupon = trim(Url::getParam('ttcoupon', ''));
        $ttaddress = trim(Url::getParam('ttaddress', ''));
        $ttDateSend = trim(Url::getParam('ttDateSend', ''));#d/m/Y:h:i
        $ip = Url::getParam('ip', '');

        $dataBuy = trim(html_entity_decode(Url::getParam('dataBuy', '')));
        #1 vai số điện thoại cho vào black List thi ghi log ko cho mua
        if($ttphone!='' && in_array($ttphone,Cart::$arrPhone)){
            LogsApi::insert('Hack', FunctionLib::ip(), 'Khách '.$ttphone.' đặt mua tại saite'.$agent_site.' lúc: '.TIME_NOW.json_encode($dataBuy));
            $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>220,'app_id' => $agent_site);
            return System::apiError(t(isset(System::$data['status'][220]) ? System::$data['status'][220]['title'] : 'Hiện kho quà đang hết vui lòng quay lại sau.'), ['status'=>220],$saveCartErr);
        }
        $thiep_id = Url::getParamInt('thiep_id', 0);

        $arrCart = json_decode(StringLib::post_db_parse_html($dataBuy), true);
        $arrCartDetail = array();
        if (empty($arrCart)) {
            $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>221,'app_id' => $agent_site);
            $message_err = t(isset(System::$data['status'][221]) ? System::$data['status'][221]['title'] : 'Không tìm thấy sản phẩm mua');
            Sentry::cart(9,221,$app_id,$transaction_id,$message_err,array('app_name'=>$site['title'],'dataBuy'=>$dataBuy,'phone'=>$ttphone,'email'=>$ttemail,'site_user_id'=>$site_user_id,'ip_address'=>$ip));
            return System::apiError($message_err, ['status'=>221],$saveCartErr);
        }
        $send_time = 0;
        if ($ttDateSend != '') {
            $ttDateSendArr = explode('|', $ttDateSend);

            $arrTime = array();
            $arrDate = array();

            $textDate = '';
            $textTime = '8:30';

            if (!empty($ttDateSendArr)) {
                if (isset($ttDateSendArr[0])) {
                    $textDate = $ttDateSendArr[0];
                }
                if (isset($ttDateSendArr[1])) {
                    $textTime = $ttDateSendArr[1];
                }
            }

            if ($textDate != '') {
                $arrDate = explode('/', $textDate);
            }

            if ($textTime != '') {
                $arrTime = explode(':', $textDate);
            }

            if (!empty($arrDate) && !empty($arrTime)) {
                $send_time = mktime((int)$arrTime[0], (int)$arrTime[1], 0, (int)$arrDate[1], (int)$arrDate[0], (int)$arrDate[2]);
            }
        }

        $ttemail = Customer::fixEmail($ttemail);

        /* Kiểm tra đầu vào nếu truyền  Signature*/
        $signature = Url::getParam('Signature', '');
        $internal = Url::getParamInt('Internal', 0);#Khi Internal = 1 thì sẽ lấy public key thành their_key
        if($site["their_key"]!=''){
            $dataSignature = array(
                'app_secret' => $app_secret,
                'app_id' => $app_id,
                'site_user_id' => $site_user_id,
                'transaction_id' => $transaction_id,
                'dataBuy' => $arrCart,
                'isSendSms' => $isSendSms
            );
            if($site['is_pom'] == 2){
                $dataSignature['campaign_code']= $campaign_code;
            }
            ksort($dataSignature);
            if($internal==1){
                $site["their_key"] = $site["public_key"];
            }
            if (!RSA::verifyRSA(json_encode($dataSignature), base64_decode($signature), $site["their_key"])) {
                $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>213,'app_id' => $agent_site);
                return System::apiError(t(isset(System::$data['status'][213]) ? System::$data['status'][213]['title'] : 'Xảy ra lỗi xác thực.'), ['status'=>213],$saveCartErr);
            }
        }
        /* End Kiểm tra đầu vào nếu truyền  Signature*/
        #Kiem tra $transaction_id neu co
        #Se xoa cac don hang yeu cau xoa va tao lai
        if ($transaction_id != '') {
            $check_transaction_site = Cart::check_transaction_site($agent_site, $transaction_id,$shipping_info_available,$shorten);
            if ($check_transaction_site) {
                if($callback_transaction == 1 && $check_transaction_site['pay'] == 1){
                    DB::update(T_CART,array('transaction_id'=>'DELETETRANSACTION'),'id='.$check_transaction_site['cart']['id']);
                }else{
                    FunctionLib::redisFlag($nameOneTranSite,2);
                    return System::apiSuccess($check_transaction_site);
                }
            }
        }else{
            $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>403,'app_id' => $agent_site);
            return System::apiError(t(isset(System::$data['status'][403]) ? System::$data['status'][403]['title'] : 'Không tìm thấy Mã Giao Dịch (transaction_id).'), ['status'=>403],$saveCartErr);
        }
        $arrGiftId = array();
        $isPhysical = false;
        $isCallCartNew = false;
        if (!empty($arrCart)) {
            $giftDetailID = array();
            $quantityValidate = 0;
            foreach ($arrCart as $key => $item) {
                $item['quantity']  = (int) $item['quantity'];
                $item['priceId']  = (int) $item['priceId'];
                if ($item['quantity'] <= 0) {
                    $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>404,'app_id' => $agent_site);
                    return System::apiError(t(isset(System::$data['status'][404]) ? System::$data['status'][404]['title'] : 'Số lượng phải lớn hơn 0'), ['status'=>404],$saveCartErr);
                } else {
                    if (FunctionLib::check_number($item['priceId'])) {
                        $price_idPUSH = (int) $item['priceId'];
                        $quantityPUSH = (int) $item['quantity'];
                        $arrCartDetail[$price_idPUSH] = array('quantity' => $quantityPUSH);
                        $giftDetailID[$price_idPUSH] = $price_idPUSH;
                        $quantityValidate += $quantityPUSH;
                        if (!isset($item['id']) || (int)$item['id'] == 0) {

                            /*$giftID = DB::fetch("SELECT gift_id from " . T_GIFT_DETAIL . " WHERE id=" . ((int)$item['priceId']) . " LIMIT  1");
                            if(!empty($giftID)){
                                $arrGiftId[$giftID['gift_id']] = $giftID['gift_id'];
                                $arrCart[$key]['id'] = $giftID['gift_id'];
                            }*/
                        } else {
                            if ($item['id'] > 0) {
                                $arrGiftId[$item['id']] = $item['id'];
                            }
                        }
                    } else {
                        $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>222,'app_id' => $agent_site);
                        $message_err = t(isset(System::$data['status'][222]) ? System::$data['status'][222]['title'] : 'Không tìm thấy sản phẩm');
                        Sentry::cart(9,222,$app_id,$transaction_id,$message_err,array('app_name'=>$site['title'],'dataBuy'=>$dataBuy,'phone'=>$ttphone,'email'=>$ttemail,'site_user_id'=>$site_user_id,'ip_address'=>$ip));
                        return System::apiError($message_err, ['status'=>222],$saveCartErr);
                    }
                }
                $arrCart[$key]['quantity']  =  $item['quantity'];
                $arrCart[$key]['priceId']  =  $item['priceId'];
            }
            if($quantityValidate > MAX_BUY_CART){
                $message_err = t(isset(System::$data['status'][410]) ? System::$data['status'][410]['title'] : 'Đơn hàng của bạn đang vượt số lượng cho phép (30 sản phẩm/ đơn hàng). Vui lòng thử lại ');
                Sentry::cart(4,410,$app_id,$transaction_id,$message_err,array('app_name'=>$site['title'],'dataBuy'=>$dataBuy,'phone'=>$ttphone,'email'=>$ttemail,'site_user_id'=>$site_user_id,'ip_address'=>$ip));
                $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>410,'app_id' => $agent_site);
                return System::apiError($message_err, ['status'=>410],$saveCartErr);
            }
            if(!empty($giftDetailID)){
                $giftIDArr = DB::query("SELECT gift_id,id from " . T_GIFT_DETAIL . " WHERE id IN (" . implode(',',$giftDetailID) .')');
                if($giftIDArr){
                    while ( $row = @mysql_fetch_assoc ( $giftIDArr ) ) {
                        $arrGiftId[$row['gift_id']] = $row['gift_id'];
                        foreach ($arrCart as $key => $item) {
                            if ($item['priceId'] == $row['id']) {
                                $arrCart[$key]['id'] = $row['gift_id'];
                            }
                        }
                    }
                }
            }
            if (!empty($arrGiftId)) {
                $gift_check = array();
                $regift_check = DB::query("SELECT id,isPhysical,type FROM " . T_GIFT . " WHERE status IN(1,2) AND id IN(" . implode(',', $arrGiftId) . ")");
                if ($regift_check) {
                    while ($rowGC = mysql_fetch_array($regift_check)) {
                        $gift_check[$rowGC['id']] = $rowGC;
                        if(in_array($rowGC['type'],[19,14,21,22,23]) || ($rowGC['type'] == 8 && FunctionLib::passInternal())){
                            $isCallCartNew = true;
                        }
                        if($rowGC['isPhysical'] == 2 || $rowGC['type'] == 9){
                            $isPhysical = true;
                        }
                    }
                }
                if (count($gift_check) != count($arrGiftId)) {
                    $message_err = t(isset(System::$data['status'][223]) ? System::$data['status'][223]['title'] : '1 trong số quà tặng bạn đặt mua đã hết hạn, bạn hãy chọn lại quà khác.').implode(',', $arrGiftId);
                    Sentry::cart(1,410,$app_id,$transaction_id,$message_err,array('app_name'=>$site['title'],'dataBuy'=>$dataBuy,'phone'=>$ttphone,'email'=>$ttemail,'site_user_id'=>$site_user_id,'ip_address'=>$ip));
                    $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>223,'app_id' => $agent_site);
                    return System::apiError($message_err, ['status'=>223],$saveCartErr);
                }
            } else {
                $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>224,'app_id' => $agent_site);
                $message_err = t(isset(System::$data['status'][224]) ? System::$data['status'][224]['title'] : 'Không tìm thấy quà tặng');
                Sentry::cart(9,224,$app_id,$transaction_id,$message_err,array('app_name'=>$site['title'],'dataBuy'=>$dataBuy,'phone'=>$ttphone,'email'=>$ttemail,'site_user_id'=>$site_user_id,'ip_address'=>$ip));
                return System::apiError($message_err, ['status'=>224],$saveCartErr);
            }
        } else {
            $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>405,'app_id' => $agent_site);
            $message_err = t(isset(System::$data['status'][405]) ? System::$data['status'][405]['title'] : 'Bạn vui lòng chọn quà muốn tặng trước.');
            Sentry::cart(9,405,$app_id,$transaction_id,$message_err,array('app_name'=>$site['title'],'dataBuy'=>$dataBuy,'phone'=>$ttphone,'email'=>$ttemail,'site_user_id'=>$site_user_id,'ip_address'=>$ip));
            return System::apiError($message_err, ['status'=>405],$saveCartErr);
        }
        #Ngày 19-6-2023 Làm thêm tính năng Call back đối với TOPUP
        $callback = trim(html_entity_decode(Url::getParam('callback', '')));
        $arrCallback = json_decode(StringLib::post_db_parse_html($callback), true);
        #END
        #Ngày 25-10-2022 them phan ban đơn hàng mới
        if($isCallCartNew){
            FunctionLib::redisFlag($nameOneTranSite,2);
            return Cart::callCartPayVoucher(
                [
                    'app_secret' => $app_secret,
                    'app_id' => $app_id,
                    'transaction_id' => $transaction_id,
                    'dataBuy' => $arrCart,
                    'site_user_id' => $site_user_id,
                    'shorten' => $shorten,
                    'isSendSms' => $isSendSms,
                    'ttemail' => $ttemail,
                    'ttphone' => $ttphone,
                    'pin' => $pin,
                    'ip' => $ip,
                    'urcard_id' => $urcard_id,
                    'wallet_id' => $wallet_id,
                    'channel' => $channel,
                    'callback' => $arrCallback,
                    'firstname' => $firstname,
                    'lastname' => $lastname,
                    'ttfullname' => $ttfullname,
                    'member_id' => $member_id,
                    'campaign_code' => $campaign_code,
                    'subPOCode' => $subPOCode,
                    'partnerPOCode' => $partnerPOCode,
                    'ttmessage' => $ttmessage,
                    'metadata' => trim(html_entity_decode($metadata))
                ]
            );
        }
        #END
        if ($ttemail != '' && !FunctionLib::is_valid_email($ttemail)) {
            $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>304,'app_id' => $agent_site);
            return System::apiError(t(isset(System::$data['status'][304]) ? System::$data['status'][304]['title'] : 'Email không đúng định dạng'), ['status'=>304],$saveCartErr);
        }

        if ($ttcoupon != '' && !Coupon::isValid($ttcoupon, $arrGiftId)) {
            $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>406,'app_id' => $agent_site);
            return System::apiError(t(isset(System::$data['status'][406]) ? System::$data['status'][406]['title'] : 'Mã khuyến mại không đúng'), ['status'=>406],$saveCartErr);
        }

        $city_id = Url::getParamInt('city_id',0);
        $district_id = Url::getParamInt('district_id',0);
        $ward_id = Url::getParamInt('ward_id',0);
        $address_id = 0;
        #Chay rieng cho MBank
        $city_id_mb = Url::getParamInt('city_id_mb',0);
        $district_id_mb = Url::getParamInt('district_id_mb',0);
        if($city_id_mb > 0){
            $city_id = DB::fetch("SELECT id FROM ".T_PROVINCE." WHERE mb_id=".$city_id_mb,'id',0);
        }
        if($district_id_mb > 0){
            $district_id = DB::fetch("SELECT id FROM ".T_DISTRICT." WHERE mb_id=".$district_id_mb,'id',0);
        }
        #Chay rieng cho Bao Viet
        $shipping_location = Url::getParam('shipping_location', '');
        if ($shipping_location != '') {
            $arr_shipping_location = explode(',', $shipping_location);
            if (isset($arr_shipping_location[0]) && $arr_shipping_location[0] != '' && isset($arr_shipping_location[2]) && isset($arr_shipping_location[2])) {
                $ward_partner = DB::fetch('SELECT * FROM province_partner where title_ward="' . trim($arr_shipping_location[0]) . '" AND title_district="' . trim($arr_shipping_location[1]) . '" AND title_province="' . trim($arr_shipping_location[2]) . '"');
                if (!empty($ward_partner)) {
                    $city_id = $ward_partner['province_id'];
                    $district_id = $ward_partner['district_id'];
                    $ward = DB::fetch("SELECT * FROM ward where province_id=" . $city_id . " AND district_id=" . $district_id . " AND (title LIKE '%" . trim($arr_shipping_location[0]) . "%' OR title_full LIKE '%" . trim($arr_shipping_location[0]) . "%')");
                    if (!empty($ward)) {
                        $ward_id = $ward['id'];
                    }
                }
            }
        }
        #End
        if ($ttphone != '' && !FunctionLib::is_phone($ttphone)) {
            $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>610,'app_id' => $agent_site);
            return System::apiError(t('Không tìm thấy số điện thoại'), ['status'=>610],$saveCartErr);
        }
        if ($ttemail != '' && !FunctionLib::is_email($ttemail)) {
            $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>304,'app_id' => $agent_site);
            return System::apiError(t('Không tìm thấy Email'), ['status'=>304],$saveCartErr);
        }
        $delivery_note = Url::getParam('delivery_note','');
        $address_id = Url::getParam('address_id',0);
        if($isPhysical == true && $shipping_info_available == 2 && $address_id==0){
            #Can Validate Các trường
            if ($city_id == 0) {
                $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>602,'app_id' => $agent_site);
                return System::apiError(t('Không tìm thấy mã tỉnh thành phố'), ['status'=>602],$saveCartErr);
            }
            $city = DB::fetch("SELECT id FROM ".T_PROVINCE." WHERE id=".$city_id);
            if(empty($city)){
                $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>603,'app_id' => $agent_site);
                return System::apiError(t('Tỉnh thành bạn nhập không tồn tại'), ['status'=>603],$saveCartErr);
            }
            if ($district_id == 0) {
                $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>604,'app_id' => $agent_site);
                return System::apiError(t('Không tìm thấy mã quận huyện'), ['status'=>604],$saveCartErr);
            }
            $district = DB::fetch("SELECT id,city_id FROM " . T_DISTRICT . " WHERE id=" . $district_id);
            if (empty($district)) {
                $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>605,'app_id' => $agent_site);
                return System::apiError(t('Quận huyện bạn nhập không tồn tại'), ['status'=>605],$saveCartErr);
            }
            if($district['city_id'] != $city_id){
                $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>606,'app_id' => $agent_site);
                return System::apiError(t('Quận huyện không thuộc thành phố bạn chọn'), ['status'=>606],$saveCartErr);
            }
            if($shipping_location == '' && $site['id']!=500000058 && $site['id']!=226 && $site['id']!=254){
                if ($ward_id == 0) {
                    $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>607,'app_id' => $agent_site);
                    return System::apiError(t('Không tìm thấy mã phường xã'), ['status'=>607],$saveCartErr);
                }
                $ward = DB::fetch("SELECT id,district_id FROM " . T_WARD . " WHERE id=" . $ward_id);
                if (empty($ward)) {
                    $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>608,'app_id' => $agent_site);
                    return System::apiError(t('Phường xã bạn nhập không tồn tại'), ['status'=>608],$saveCartErr);
                }
                if($ward['district_id'] != $district_id){
                    $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>609,'app_id' => $agent_site);
                    return System::apiError(t('Xã phường không thuộc tỉnh thành bạn chọn'), ['status'=>609],$saveCartErr);
                }
            }
            if ($ttaddress == '') {
                $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>601,'app_id' => $agent_site);
                return System::apiError(t('Không tìm thấy địa chỉ'), ['status'=>601],$saveCartErr);
            }
            if ($ttphone == '' || !FunctionLib::is_phone($ttphone)) {
                $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>610,'app_id' => $agent_site);
                return System::apiError(t('Không tìm thấy số điện thoại'), ['status'=>610],$saveCartErr);
            }
        }
        if($shipping_info_available == 2 && $address_id == 0){
            if($ttphone!='' || $ttemail!=''){
                $receiver = Receiver::add(array(
                    'first_name' => $ttfullname,
                    'fullname' => $ttfullname,
                    'email' => $ttemail,
                    'phone' => $ttphone,
                    'indentify' => $ttindentify,
                ));
                if($receiver){
                    $entity = [
                        "fullname" => $ttfullname,
                        "phone" => System::encrypt($ttphone),
                        "email" => System::encrypt($ttemail),
                        "city_id" => $city_id,
                        "ward_id" => $ward_id,
                        "number" => $ttaddress,
                        "district_id" => $district_id,
                        "receiver_id" => $receiver['id'],
                        "status" => 2
                    ];
                    $address_id = Cart::saveAddress($entity);
                }
            }
        }
        #END
        #Lay thong tin nguoi dang nhap neu co
        /*if ($customer_id > 0) {
            $customer = Customer::get($customer_id);
        }*/
        $detailGifts = array();
        if (!empty($arrCartDetail)) {
            $detailGifts = DB::fetch_all("SELECT id,code_quantity,justGetOrder,gift_id,valuex,promotion_code FROM " . T_GIFT_DETAIL . " WHERE id IN(".implode(',', array_keys($arrCartDetail)).") ORDER BY position ASC");
            foreach ($detailGifts as $k => $quan) {
                if (in_array($quan['gift_id'], $arrGiftId)) {
                    if ($quan['code_quantity'] < $arrCartDetail[$quan['id']]['quantity']) {
                        $gift = DB::fetch("SELECT id FROM ".T_GIFT." WHERE id=".$quan['gift_id']);
                        $message_err = t(isset(System::$data['status'][407]) ? System::$data['status'][407]['title'] : 'Số lượng sản phẩm không đủ');
                        Sentry::cart(1,407,$app_id,$transaction_id,$message_err,array('app_name'=>$site['title'],'dataBuy'=>$dataBuy,'phone'=>$ttphone,'email'=>$ttemail,'site_user_id'=>$site_user_id,'ip_address'=>$ip));
                        $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>407,'app_id' => $agent_site);
                        return System::apiError($message_err, ['status'=>407,'gift' => $gift],$saveCartErr);
                    }
                } else {
                    $message_err = t(isset(System::$data['status'][223]) ? System::$data['status'][223]['title'] : '1 trong số quà tặng bạn đặt mua đã hết hạn, bạn hãy chọn lại quà khác.');
                    Sentry::cart(1,223,$app_id,$transaction_id,$message_err,array('app_name'=>$site['title'],'dataBuy'=>$dataBuy,'phone'=>$ttphone,'email'=>$ttemail,'site_user_id'=>$site_user_id,'ip_address'=>$ip));
                    $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>223,'app_id' => $agent_site);
                    return System::apiError(223, ['status'=>223],$saveCartErr);
                }
            }
        }
        #Kiem tra qua ko co trong Bo Qua khong cho mua
        if($site['gift_id']>0 && !empty($arrCartDetail)){
            $giftSet = Gift::giftSetbyGiftID($site['gift_id']);
            $errGiftSet = 0;
            if(!empty($giftSet)){
                foreach ($arrCartDetail as $k => $giftDe){
                    if(!isset($giftSet[$k])){
                        $errGiftSet = 1;
                    }
                }
            }else{
                $errGiftSet = 1;
            }
            if($errGiftSet == 1){
                $message_err = t(isset(System::$data['status'][226]) ? System::$data['status'][226]['title'] : 'Quà tặng không nằm trong chương trình');
                Sentry::cart(2,226,$app_id,$transaction_id,$message_err,array('app_name'=>$site['title'],'dataBuy'=>$dataBuy,'phone'=>$ttphone,'email'=>$ttemail,'site_user_id'=>$site_user_id,'ip_address'=>$ip));
                $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>226,'app_id' => $agent_site);
                return System::apiError($message_err, ['status'=>226],$saveCartErr);
            }
        }
        #END
        $emailnguoigui = trim(Url::getParam('emailnguoigui', ''));
        $nguoigui = trim(Url::getParam('nguoigui', ''));
        $phonenguoigui = trim(Url::getParam('phonenguoigui', ''));
        if($site['id'] == 124){
            $phonenguoigui = '';
        }
        $cart = array(
            'from' => array('name' => $nguoigui, 'phone' => $phonenguoigui, 'email' => $emailnguoigui, 'message' => $ttmessage, 'thiep_id' => $thiep_id),
            'to' => array('name' => $ttfullname, 'phone' => $ttphone, 'email' => $ttemail, 'indentify' => $ttindentify),
            'items' => $arrCart,
            'coupon' => $ttcoupon,
            'address' => $ttaddress,
            'send_time' => $send_time,
            'site_id' => $agent_site,
            'site_user_id' => $site_user_id,
            'ip' => $ip,
            'payment_type' => PAY_CARD,
            'is_cod' => $is_cod,
            'address_id' => $address_id,
            'delivery_note' => $delivery_note,
            'card_id' => ($site['is_pom']==2?0:$site['card_id']),
            'urcard_id' => $urcard_id,
            'wallet_id' => $wallet_id,
            'channel' => $channel
        );
        $msg = '';
        $arrCartFinal = Cart::createOrder($cart, $msg);
        if ($msg == '') {
            $msg = 'Hệ thống hiện tại không tạo được đơn hàng.';
        }
        if ($site['checkOtp'] == IS_YES && !empty($verity)) {
            DB::update(T_APP_OTP, array('process' => IS_YES), 'id=' . $verity['id']);
        }
        if (!empty($arrCartFinal)) {
            $updateCart = array();
            $updateCartDetail = array();
            #Khi tao dc don hang thi se luu transaction_id
            if ($transaction_id != '') {
                $updateCart['transaction_id'] = $transaction_id;
            }
            if ($campaign_code != '' && $site['is_pom'] == IS_YES) {
                $updateCart['campaign_code'] = $campaign_code;
            }
            if ($site['is_pom'] == 3) {
                $updateCart['campaign_code'] = $site['campaign_code'];
            }
            #Cap nhat don hang ko can gui SMS
            # Với APP APP_MONDELEZ_SMS_ZALO họ vẫn muốn nhận link code và cũng muốn mình gửi LINK QUA qua SMS
            if ($isSendSms == 0 && $site['id']!=APP_MONDELEZ_SMS_ZALO) {
                $updateCart['delivery'] = 1;
                $updateCartDetail['delivery'] = 1;
                $updateCartDetail['sendSms'] = 1;
            }
            if(!empty($updateCartDetail)){
                DB::update(T_CART_DETAIL, $updateCartDetail, 'cart_id=' . $arrCartFinal['id']);
            }
            if (!empty($updateCart)) {
                DB::update(T_CART, $updateCart, 'id=' . $arrCartFinal['id']);
                FunctionLib::redisFlag($nameOneTranSite,2);
            }
            #END
            $checkPay = false;
            if(($arrCartFinal['money_total'] == 0) || ($arrCartFinal['money_total'] > 0 && Site::check_money($agent_site, $access_urbox,$site,$arrCartFinal['money_total'],$moneyPom) )){
               $checkPay = true;
            }
            #Thanh toan qua SITE
            if ($checkPay) {
                $cart = Cart::getCart($arrCartFinal['id'],false);
                $detail_cart = array();
                if (isset($cart['detail']) && $cart['detail']) {
                    #Ngay 5-6-2019 Doi lại luong xuat code truoc khi thanh toan
                    $codeEx = array();
                    $giftIdErr = 0;
                    foreach ($cart['detail'] as $detail) {
                        #insert thêm thông tin
                        Cart::addCartDetailInfo($detail['id'],$ttfullname,$ttemail,$ttphone,$ttindentify);
                        #end
                        $code = '';
                        #$gift = DB::fetch("SELECT * FROM " . T_GIFT . " WHERE id=" . $detail['gift_id']);
                        if ($agent_site != 13 && $agent_site != 32 && $agent_site != 61) {
                            GiftCode::assignCode($detail['id'], $code,['pin'=>$pin,'cart_detail'=>$detail,'mb_code'=>$mb_code,'mb_expired'=>$mb_expired]);
                            $code = System::decrypt($code);
                            if ($code != '') {
                                $codeEx[$code] = array(
                                    'code' => $code,
                                    'brand_id' => $detail['brand_id']
                                );
                            } else {
                                $giftIdErr = $detail['gift_detail_id'];
                                break;
                            }
                        }
                    }
                    if ((count($codeEx) == count($cart['detail']))||$agent_site == 13 || $agent_site == 32 || $agent_site == 61) {
                        $subPOId = 0;
                        $payFinal = false;
                        if($site['is_pom'] != 1){
                            if($arrCartFinal['money_total']>0) {
                                $dataPayPom = Urcard::subPoDecrease($app_id, $campaign_code, $subPOCode, $partnerPOCode, $transaction_id, $arrCartFinal['id'], $arrCartFinal['money_total']);
                                if (isset($dataPayPom['status']) && $dataPayPom['status'] == 'successful') {
                                    $subPOId = $dataPayPom['subPOId'];
                                    $payFinal = true;
                                }else{
                                    GiftCode::removeCodeOutCart($arrCartFinal['id'],$cart);
                                    $message_err = t(isset(System::$data['status'][309]) ? System::$data['status'][309]['title'] : 'Ngân sách chương trình không đủ để thực hiện giao dịch');
                                    Sentry::cart(3, 309,array('app_name'=>$site['title'],'dataBuy'=>$dataBuy,'phone'=>$ttphone,'email'=>$ttemail,'site_user_id'=>$site_user_id,'ip_address'=>$ip));
                                    $saveCartErr = array('cart_id' => $arrCartFinal['id'],'transaction_id' => $transaction_id,'status_code_id'=>306,'app_id' => $agent_site);
                                    return System::apiError($message_err, ['status'=>309],$saveCartErr);
                                }
                            }else{
                                $payFinal = true;
                            }
                        }else{
                            $payFinal = Site::buy_money($agent_site, $access_urbox, $arrCartFinal['money_total'], $arrCartFinal['id'],$site);
                        }
                        if(!$payFinal){
                            #Cap nhat bo het code ra khỏi dơn hang
                            GiftCode::removeCodeOutCart($arrCartFinal['id'],$cart);
                            $message_err = t(isset(System::$data['status'][306]) ? System::$data['status'][306]['title'] : 'Hệ thống khách hàng không đủ tiền');
                            Sentry::cart(3, 306,array('app_name'=>$site['title'],'dataBuy'=>$dataBuy,'phone'=>$ttphone,'email'=>$ttemail,'site_user_id'=>$site_user_id,'ip_address'=>$ip));
                            $saveCartErr = array('cart_id' => $arrCartFinal['id'],'transaction_id' => $transaction_id,'status_code_id'=>306,'app_id' => $agent_site);
                            return System::apiError($message_err, ['status'=>306],$saveCartErr);
                        }
                        Cart::payed($arrCartFinal['id'],$cart,$subPOId);
                        Cart::codeComboExpired($cart);
                        #$cart = Cart::getCart($arrCartFinal['id']);
                        $shortenUrl = array();
                        $cartLink = array();
                        $cartCodeLink = array();
                        if (isset($cart['detail']) && $cart['detail']) {
                            $giftSusID = array();
                            $giftSusArr = array();
                            $giftCodeArr = array();
                            foreach ($cart['detail'] as $detail) {
                                $giftSusID[$detail['gift_id']] = $detail['gift_id'];
                                $giftDetailSusID[$detail['gift_detail_id']] = $detail['gift_detail_id'];
                            }
                            if(!empty($giftSusID)){
                                $giftSusArr = DB::fetch_all("SELECT id,type,brand_id,showbarcode,isPhysical,pointu_id,status,is_auto_provider_switch FROM ".T_GIFT." WHERE id IN (".implode(',',$giftSusID).")");
                            }

                            $regiftCodeArr = DB::query("SELECT id,cart_detail_id,code,status,expired,pin,serial,valid_time FROM ".T_GIFT_CODE." WHERE cart_detail_id IN (".implode(',',array_keys($cart['detail'])).")");
                            if($regiftCodeArr){
                                while ( $rowe = @mysql_fetch_assoc ( $regiftCodeArr ) ) {
                                    $giftCodeArr[$rowe['cart_detail_id']] = $rowe;
                                }
                            }
                            foreach ($cart['detail'] as $detail) {
                                $estimateDelivery = '';
                                $code = '';
                                $card_id = 0;
                                $pin = '';
                                $serial = '';
                                $expired = '';
                                $expired_time = '';
                                #$gift = DB::fetch("SELECT * FROM " . T_GIFT . " WHERE id=" . $detail['gift_id']);
                                $gift = isset($giftSusArr[$detail['gift_id']])?$giftSusArr[$detail['gift_id']]:(DB::fetch("SELECT * FROM " . T_GIFT . " WHERE id=" . $detail['gift_id']));
                                if ($agent_site == 13 ||  $agent_site == 32 || $agent_site == 61) {
                                    $link = LINK_API_URBOX . 'nhan-qua-doi-tac/' . $detail['receive_code'] . '.html';
                                } else {
                                    #$gift_code = DB::fetch("SELECT * FROM " . T_GIFT_CODE . " WHERE cart_detail_id=" . $detail['id']);
                                    $gift_code = isset($giftCodeArr[$detail['id']])?$giftCodeArr[$detail['id']]:(DB::fetch("SELECT * FROM " . T_GIFT_CODE . " WHERE cart_detail_id=" . $detail['id']));
                                    if ($gift_code) {
                                        $code = System::decrypt($gift_code['code']);
                                        $pin = $gift_code['pin'];
                                        $serial = $gift_code['serial'];
                                        $link = LINK_API_URBOX . 'nhan-qua/' . $detail['receive_code'] . '.html';
                                        if ($gift['status'] == 3) {
                                            $link = LINK_API_URBOX . 'nhan-qua-doi-tac/' . $detail['receive_code'] . '.html';
                                        }
                                        $expired_time = $gift_code['expired'];
                                        $expired = $gift_code['expired'] == 0 ? 'Vô hạn' : FunctionLib::dateFormat($gift_code['expired'], 'd/m/Y');
                                        if($gift['type'] == 4){
                                            $link = DOMAIN_LOYAL . 'card/' . System::decrypt($gift_code['code']);
                                            $cardCode = DB::fetch("SELECT id,number FROM ".T_CARD." WHERE token='".$gift_code['code']."'");
                                            if(!empty($cardCode)){
                                                $card_id = (int) $cardCode['id'];
                                                $code = $cardCode['number'];
                                            }
                                        }
                                        if($gift['type'] == 7){
                                            if(isset(System::$data['point_unit'][$gift['pointu_id']])){
                                                $link = System::$data['point_unit'][$gift['pointu_id']]['url'] . System::decrypt($gift_code['code']);
                                            }
                                        }
                                        if($gift['type'] == 12){
                                            if(isset(System::$data['point_unit'][$gift['pointu_id']])){
                                                $link = System::$data['point_unit'][$gift['pointu_id']]['url'] . $detail['receive_code'];
                                            }
                                        }
                                        if($gift['type'] == 14){#08-04 Loai qua đổi lượt quay
                                            if(isset(System::$data['point_unit'][$gift['pointu_id']])){
                                                $link = System::$data['point_unit'][$gift['pointu_id']]['url'].'?token='.Cart::genLinkTurn($cart['site_user_id'],$site['id'],$cart['created']);
                                                #Cap nhat sang Hệ thống quay so
                                                Cart::pushTurn($site['id'],$detail['id'],$gift['pointu_id'],$site_user_id,$transaction_id);
                                                #Đối với Game ACB do đối tác ko truyền lấy link rút gọn được lên Urbox tiến hành FIx cứng cho APP
                                                if($app_id == APP_GAME_ACB){
                                                    $shorten = 1;
                                                }
                                            }
                                        }
                                        $giftDetail = isset($detailGifts[$detail['gift_detail_id']])?$detailGifts[$detail['gift_detail_id']]:(DB::fetch("SELECT * FROM " . T_GIFT_DETAIL . " WHERE id=" . $detail['gift_detail_id']));
                                        if($giftDetail['justGetOrder'] == IS_YES){
                                            $link = DOMAIN_LOYAL . 'grab.reward/gift/code/' . $detail['gift_detail_id'];
                                        }
                                        if($giftDetail['promotion_code'] != ''){
                                            $code = $giftDetail['promotion_code'];
                                        }
                                        if($gift['type'] == 8){
                                            GiftCode::pushTopupService($gift['pointu_id'],array(
                                                'firstname' => $firstname,
                                                'lastname' => $lastname,
                                                'full_name' => $ttfullname,
                                                'member_id' => $member_id,
                                                'app_id' => $site['id'],
                                                'cart_id' => $cart['id'],
                                                'cart_detail_id' => $detail['id'],
                                                'money' => $detail['money'],
                                                'price' => $detail['price'],
                                                'gift_id' => $detail['gift_id'],
                                                'gift_detail_id' => $detail['gift_detail_id'],
                                                'ttphone' => $ttphone,
                                                'wallet_id' => $cart['wallet_id'],
                                                'urcard_id' => $cart['urcard_id'],
                                                'transaction_id' => $cart['transaction_id'],
                                                'callback' => $arrCallback,
                                                'is_auto_provider_switch' => $gift['is_auto_provider_switch']
                                            ));
                                        }
                                        if($gift['isPhysical'] == 2 || $gift['type'] == 9){
                                            #$code = '';
                                            $expired = '';
                                            $expired_time = '';
                                            $estimateDelivery = 'Dự kiến giao hàng từ 3 - 5 ngày làm việc.';
                                        }
                                    }
                                }
                                $shortenUrl[] = $link;
                                $code_image = '';
                                if ($isSendSms == 1 && !in_array($site['id'],array(32,36))) {#Neu da gui SMS cho khach thi ko tra ve link Qua
                                    $link = '';
                                    $code = '';
                                }else{
                                    $code_image = Cart::genImageCode($gift['showbarcode'],$gift['brand_id'],$code);
                                }
                                $cartLink[] = $link;
                                $cartCodeLink[] = array(
                                    'cart_detail_id' => $detail['id'],
                                    'code_display' => FunctionLib::code_display($gift['type'],$gift['showbarcode'],2),
                                    'code_display_type' => FunctionLib::code_display($gift['type'],$gift['showbarcode'],1),
                                    'link' => $link,
                                    'code' => $code,
                                    'card_id' =>(int) $card_id,
                                    'pin' => $pin,
                                    'serial' => $serial,
                                    'priceId' => $detail['gift_detail_id'],
                                    'gift_id' => $detail['gift_id'],
                                    'token' => $detail['receive_code'],
                                    'expired' => $expired,
                                    'expired_time' => (int) $expired_time,
                                    'valid_time' => (int) $gift_code['valid_time'],
                                    'code_image' => $gift['isPhysical']==1?$code_image:'',
                                    'estimateDelivery' => t($estimateDelivery),
                                    'ttemail' => $ttemail,
                                    'ttphone' => $ttphone,
                                    'receive_code' => $detail['receive_code'],
                                    'city_id' => $city_id,
                                    'district_id' => $district_id,
                                    'ward_id' => $ward_id,
                                    'delivery_note' => $delivery_note,
                                    'ttaddress' => $ttaddress,
                                    'deliveryCode' => (int) $detail['delivery'],
                                    'type' => (int) $detail['type'],
                                    'urcard_id' => $urcard_id,
                                    'price' => (int) $detail['price']
                                );
                            }
                        }
                        $key = md5($cart['id'] . $cart['customer_id'] . $cart['receiver_id'] . $cart['email'] . $cart['phone'] . 'KIEMTRAMONQUACHUAN');
                        $linkCart = LINK_API_URBOX.'cart/'.$key.'.html?cart_no='.$cart['id'];
                        $linkShippingInfo = '';
                        if($shipping_info_available == 1 && $isPhysical){
                            $linkShippingInfo = DOMAIN_LOYAL . 'delivery/' . $key.'?cart_no='.$cart['id'];
                            $shortenUrl[] = $linkShippingInfo;
                        }
                        $linkCombo = '';
                        if($cart['combo_id'] > 0 ){
                            $linkCombo = DOMAIN_LOYAL . 'combo/'. $cart['token'];
                            $shortenUrl[] = $linkCombo;
                        }
                        $shortenUrl[] = $linkCart;
                        $shortenUrlData = array();
                        if($shorten == 1){
                            $shortenUrlData = Shorten::buildMuti($shortenUrl);
                            if(!empty($cartLink)){
                                foreach ($cartLink as &$linkShort){
                                    if(isset($shortenUrlData[$linkShort])) {
                                        $linkShort = $shortenUrlData[$linkShort];
                                    }
                                }
                            }
                            if(!empty($cartCodeLink)){
                                foreach ($cartCodeLink as &$linkShort2){
                                    if(isset($shortenUrlData[$linkShort2['link']])) {
                                        $linkShort2['link'] = $shortenUrlData[$linkShort2['link']];
                                    }
                                }
                            }
                        }
                        return System::apiSuccess(
                            array(
                                'pay' => IS_YES,
                                'transaction_id' => $transaction_id,
                                'cart_created' => $cart['created'],
                                'campaign_code' => $cart['campaign_code'],
                                'linkCart' => (($shorten == 1 && isset($shortenUrlData[$linkCart])) ? $shortenUrlData[$linkCart] : $linkCart),
                                'linkCombo' => (($shorten == 1 && isset($shortenUrlData[$linkCombo])) ? $shortenUrlData[$linkCombo] : $linkCombo),
                                'linkShippingInfo' => (($shorten == 1 && isset($shortenUrlData[$linkShippingInfo])) ? $shortenUrlData[$linkShippingInfo] : $linkShippingInfo),
                                'cart' =>
                                    array(
                                        'id' => $cart['id'],
                                        'cartNo' => $cart['id'],
                                        'money_total' => $cart['money_total'],
                                        'money_ship' => $cart['money_ship'],
                                        'link_gift' => $cartLink,
                                        'code_link_gift' => $cartCodeLink
                                    )
                            )
                        );
                    } else {
                        #Cap nhat bo het code ra khỏi dơn hang
                        GiftCode::removeCodeOutCart($arrCartFinal['id'],$cart);
                        $message_err = t('Sản phẩm ') . DB::fetch("SELECT title FROM " . T_GIFT_DETAIL . " WHERE id=" . $giftIdErr, 'title') . t(" đang hết, vui lòng bỏ sản phẩm ra khỏi giỏ hàng.");
                        Sentry::cart(1,225,$app_id,$transaction_id,$message_err,array('app_name'=>$site['title'],'dataBuy'=>$dataBuy,'phone'=>$ttphone,'email'=>$ttemail,'site_user_id'=>$site_user_id,'ip_address'=>$ip));
                        $saveCartErr = array('cart_id' => $arrCartFinal['id'],'transaction_id' => $transaction_id,'status_code_id'=>225,'app_id' => $agent_site);
                        return System::apiError($message_err, ['status'=>225],$saveCartErr);
                    }
                }
            } else {
                if($site['is_pom'] == 1){
                    $message_err = t(isset(System::$data['status'][306]) ? System::$data['status'][306]['title'] : 'Hệ thống khách hàng không đủ tiền');
                    Sentry::cart(8,306,$app_id,$transaction_id,$message_err,array('app_name'=>$site['title'],'dataBuy'=>$dataBuy,'phone'=>$ttphone,'email'=>$ttemail,'site_user_id'=>$site_user_id,'ip_address'=>$ip));
                    $saveCartErr = array('cart_id' => $arrCartFinal['id'],'transaction_id' => $transaction_id,'status_code_id'=>306,'app_id' => $agent_site);
                    return System::apiError($message_err, ['status'=>306],$saveCartErr);

                }else{
                    $message_err = t(isset(System::$data['status'][309]) ? System::$data['status'][309]['title'] : 'Ngân sách chương trình không đủ để thực hiện giao dịch');
                    Sentry::cart(8,306,$app_id,$transaction_id,$message_err,array('app_name'=>$site['title'],'dataBuy'=>$dataBuy,'phone'=>$ttphone,'email'=>$ttemail,'site_user_id'=>$site_user_id,'ip_address'=>$ip));
                    $saveCartErr = array('cart_id' => $arrCartFinal['id'],'transaction_id' => $transaction_id,'status_code_id'=>309,'app_id' => $agent_site);
                    return System::apiError($message_err, ['status'=>309],$saveCartErr);
                    
                }
            }
        }
        $saveCartErr = array('cart_id' => 0,'transaction_id' => $transaction_id,'status_code_id'=>408,'app_id' => $agent_site);
        return System::apiError(t(isset(System::$data['status'][408]) ? System::$data['status'][408]['title'] : $msg), ['status'=>408],$saveCartErr);
    }

    function cartLoyal()
    {
die;
        #Kiem tra access_urbox
        $access_urbox = Url::getParam('access_urbox', '');
        $agent_site = Url::getParam('agent_site', '');
        #Tat cua VIETTEL
        #if ($agent_site == '15') {
        #    return System::apiError('Hệ thống đang tạm dừng hoặt động để nâng cấp.', false);
        #}
        if (!Site::check_access_urbox($agent_site, $access_urbox)) {
            return System::apiError('Access Urbox không đúng.', false);
        }

        $site = DB::fetch("SELECT * FROM " . T_APP . " WHERE id=" . $agent_site);
        $customer_id = 0;
        if ($site) {
            $customer_id = $site['customer_id'];
        }

        #Lay thong tin don hang

        $ttmessage = trim(Url::getParam('ttmessage', ''));
        $ttemail = trim(Url::getParam('ttemail', ''));
        $ttphone = trim(Url::getParam('ttphone', ''));
        $ttfullname = trim(Url::getParam('ttfullname', ''));
        $ttcoupon = trim(Url::getParam('ttcoupon', ''));
        $ttaddress = trim(Url::getParam('ttaddress', ''));
        $ttDateSend = trim(Url::getParam('ttDateSend', ''));
        $address_id = Url::getParamInt('address_id', 0);

        $dataBuy = trim(html_entity_decode(Url::getParam('dataBuy', '')));

        $thiep_id = Url::getParamInt('thiep_id', 0);

        $arrCart = json_decode(StringLib::post_db_parse_html($dataBuy), true);
        $arrCartDetail = array();
        if (empty($arrCart)) {
            System::apiError('Không tìm thấy sản phẩm mua.', false);
        }
        $send_time = 0;
        if ($ttDateSend != '') {
            $arrTime = array(0 => 8, 1 => 30);
            $arrDate = array();
            if (isset($arrDateTime[0])) {
                $arrDate = explode('/', $ttDateSend);
            }

            if (!empty($arrDate) && !empty($arrTime)) {
                $send_time = mktime((int)$arrTime[0], (int)$arrTime[1], 0, (int)$arrDate[1], (int)$arrDate[0], (int)$arrDate[2]);
            }
        }

        $ttemail = Customer::fixEmail($ttemail);

        $arrGiftId = array();
        if (!empty($arrCart)) {
            foreach ($arrCart as $key => $item) {
                if ($item['quantity'] <= 0) {
                    System::apiError('Số lượng phải lớn hơn 0', false);
                } else {
                    $arrCartDetail[$item['priceId']] = array('quantity' => $item['quantity']);
                    $arrGiftId[$item['id']] = $item['id'];
                }
            }
            if (!empty($arrGiftId)) {
                $gift_check = DB::count(T_GIFT, 'status IN(1,2,3) AND id IN(' . implode(',', $arrGiftId) . ')');
                if ($gift_check != count($arrGiftId)) {
                    System::apiError('1 trong số quà tặng bạn đặt mua đã hết hạn, bạn hãy chọn lại quà khác.', false);
                }
            } else {
                System::apiError('Không tìm thấy quà tặng', false);
            }
        } else {
            System::apiError('Bạn vui lòng chọn quà muốn tặng trước.', false);
        }

        if ($ttemail != '' && !FunctionLib::is_valid_email($ttemail)) {
            return System::apiError('Email không đúng định dạng', false);
        }

        if ($ttcoupon != '' && !Coupon::isValid($ttcoupon, $arrGiftId)) {
            return System::apiError('Mã khuyến mại không đúng', false);
        }

        #Lay thong tin nguoi dang nhap
        if ($customer_id > 0) {
            $customer = Customer::get($customer_id);
        }
        if (!empty($arrCartDetail)) {
            $detailGifts = Gift::getDetailByID(array_keys($arrCartDetail));

            foreach ($detailGifts as $k => $quan) {
                if (in_array($quan['gift_id'], $arrGiftId)) {
                    if ($quan['code_quantity'] < $arrCartDetail[$quan['id']]['quantity']) {
                        $gift = Gift::get($quan['gift_id']);
                        System::apiError('Số lượng sản phẩm không đủ', array('gift' => $gift));
                    }
                } else {
                    System::apiError('1 trong số quà tặng bạn đặt mua đã hết hạn, bạn hãy chọn lại quà khác.', false);
                }
            }
        }

        $emailnguoigui = '';
        $nguoigui = '';
        $phonenguoigui = '';

        if (!empty($customer)) {
            $emailnguoigui = $customer['email'];
            $nguoigui = $customer['phone'];
            $phonenguoigui = $customer['first_name'] . ' ' . $customer['last_name'];
        }

        $cart = array(
            'from' => array('name' => $nguoigui, 'phone' => $phonenguoigui, 'email' => $emailnguoigui, 'message' => $ttmessage, 'thiep_id' => $thiep_id),
            'to' => array('name' => $ttfullname, 'phone' => $ttphone, 'email' => $ttemail),
            'items' => $arrCart,
            'coupon' => $ttcoupon,
            'address' => $ttaddress,
            'send_time' => $send_time,
            'site_id' => $agent_site,
            'address_id' => $address_id,
            'payment_type' => PAY_VISA_MASTER
        );
        $msg = '';
        $arrCartFinal = Cart::createOrder($cart, $msg);
        if ($msg == '') {
            $msg = 'Hệ thống hiện tại không tạo được đơn hàng.';
        }
        if (!empty($arrCartFinal)) {
            #Khong can thanh toan
            if ($arrCartFinal['money_total'] <= 0) {
                #Cap nhat don hang thanh cong
                Cart::payed($arrCartFinal['id']);
                $cart = Cart::get($arrCartFinal['id']);
                $detail_cart = array();
                if (isset($cart['detail']) && $cart['detail']) {
                    foreach ($cart['detail'] as $detail) {
                        GiftCode::assignCode($detail['id'], $code);
                        $link = LINK_API_URBOX . 'nhan-qua-doi-tac/' . $detail['receive_code'] . '.html';
                        $detail_cart[] = array('link' => $link, 'id' => $detail['id']);

                    }
                    DB::update(T_CART, array('delivery' => 1, 'smsthankiu' => 1), 'id=' . $arrCartFinal['id']);
                    DB::update(T_CART_DETAIL, array('delivery' => 1, 'sendSms' => 1), 'cart_id=' . $arrCartFinal['id']);
                    $arrLog = array('cart_id' => $arrCartFinal['id'], 'user_id' => 0, 'action_from' => 5, 'action_to' => 2, 'action_time' => TIME_NOW, 'action_type' => 8);
                    Cart::insertLogCart($arrLog);
                }

                return System::apiSuccess(array('pay' => 1, 'access_urbox' => $access_urbox, 'agent_site' => $agent_site, 'cart' => array('id' => $cart['id'], 'cartNo' => $cart['id'], 'money_total' => $cart['money_total'], 'detail_cart' => $detail_cart)));
            } else {
                #Thanh toan qua SITE
                if (Site::check_money($agent_site, $access_urbox)) {
                    if (Site::buy_money($agent_site, $access_urbox, $arrCartFinal['money_total'], $arrCartFinal['id'])) {

                        Cart::payed($arrCartFinal['id']);
                        $cart = Cart::get($arrCartFinal['id']);
                        $detail_cart = array();
                        if (isset($cart['detail']) && $cart['detail']) {
                            foreach ($cart['detail'] as $detail) {
                                GiftCode::assignCode($detail['id'], $code);
                                $link = LINK_API_URBOX . 'nhan-qua-doi-tac/' . $detail['receive_code'] . '.html';
                                $detail_cart[] = array('link' => $link, 'id' => $detail['id']);
                            }
                            DB::update(T_CART, array('delivery' => 1, 'smsthankiu' => 1), 'id=' . $arrCartFinal['id']);
                            DB::update(T_CART_DETAIL, array('delivery' => 1, 'sendSms' => 1), 'cart_id=' . $arrCartFinal['id']);
                            $arrLog = array('cart_id' => $arrCartFinal['id'], 'user_id' => 0, 'action_from' => 5, 'action_to' => 2, 'action_time' => TIME_NOW, 'action_type' => 8);
                            Cart::insertLogCart($arrLog);
                        }

                        return System::apiSuccess(array('pay' => 1, 'access_urbox' => $access_urbox, 'agent_site' => $agent_site, 'cart' => array('id' => $cart['id'], 'cartNo' => $cart['id'], 'money_total' => $cart['money_total'], 'detail_cart' => $detail_cart)));
                    } else {
                        return System::apiError('Hệ thống khách hàng không đủ tiền', false);
                    }
                } else {
                    return System::apiError('Hệ thống khách hàng không đủ tiền', false);
                }
            }
        }
        return System::apiError($msg, false);


    }

    function cartApp()
    {
        $access_urbox = Url::getParam('access_urbox', '');
        $agent_site = Url::getParam('agent_site', '');
        $AppId = Url::getParamInt('App-Id', 0);
        $wallet_id = Url::getParamInt('wallet_id', 0);
        $channel = Url::getParamInt('channel', 1);
        $card_id = Url::getParamInt('card_id', 0);
        $transaction_id = Url::getParam('transaction_id', '');
        #Ngày 8-11-2024 Làm thêm tính năng Call back đối với TOPUP
        $callback = trim(html_entity_decode(Url::getParam('callback', '')));
        $arrCallback = json_decode(StringLib::post_db_parse_html($callback), true);
        #END
        if(!Card::startPayByRedis($card_id)){
            $saveCartErr = array('cart_id' => 0,'transaction_id' => '','status_code_id'=>512,'app_id' => $agent_site);
            return System::apiError((isset(System::$data['status'][512]) ? System::$data['status'][512]['title'] : 'Thẻ đang tiến hành thanh toán. Vui lòng đợi trong giây lát để thực hiện lại.'), ['status'=>512],$saveCartErr);
            # return System::apiError('Thẻ đang tiến hành thanh toán. Vui lòng đợi trong giây lát để thực hiện lại.', false);
        }
        if(!FunctionLib::passInternal()){
            $signature = Url::getParam('Signature', '');
            $version = Url::getParamInt('App-Version', 1600);
            $time = Url::getParamInt('App-Time-Now', 0);
            $sign = md5('APP|' . $version . '|' . $time);
            $jwt_authorization = Url::getParam('Authorization', '');
            $isValid = JwtService::validateForCartApp($jwt_authorization, $card_id, $AppId, $time,$wallet_id);
            if(!$isValid){
                Card::openPayRedis($card_id);
                $saveCartErr = array('cart_id' => 0,'transaction_id' => '','status_code_id'=>213,'app_id' => $agent_site);
                return System::apiError(t(isset(System::$data['status'][213]) ? System::$data['status'][213]['title'] : 'Xảy ra lỗi xác thực.'), ['status'=>213],$saveCartErr);
            }
            if($sign != $signature && (TIME_NOW - $time) <= 10){
                Card::openPayRedis($card_id);
                $saveCartErr = array('cart_id' => 0,'transaction_id' => '','status_code_id'=>213,'app_id' => $agent_site);
                return System::apiError(t(isset(System::$data['status'][213]) ? System::$data['status'][213]['title'] : 'Xảy ra lỗi xác thực.'), ['status'=>213],$saveCartErr);
            }
        }

        if (!Site::check_access_urbox($agent_site, $access_urbox,$site)) {
            Card::openPayRedis($card_id);
            $saveCartErr = array('cart_id' => 0,'transaction_id' => '','status_code_id'=>211,'app_id' => $agent_site);
            return System::apiError((isset(System::$data['status'][211]) ? System::$data['status'][211]['title'] : 'Access Urbox không đúng.'), ['status'=>211],$saveCartErr);
            #return System::apiError('Access Urbox không đúng.', false);
        }
        $agent_site = $site['id'];
        #Kiem tra the
        $card_id = Url::getParamInt('card_id', 0);
        if ($card_id <= 0) {
            Card::openPayRedis($card_id);
            $saveCartErr = array('cart_id' => 0,'transaction_id' => '','status_code_id'=>511,'app_id' => $agent_site);
            return System::apiError((isset(System::$data['status'][511]) ? System::$data['status'][511]['title'] : 'Không tìm thấy thông tin thẻ.'), ['status'=>511],$saveCartErr);
            #return System::apiError('Không tìm thấy thông tin thẻ.', false);
        }
        $card = DB::fetch("SELECT * FROM " . T_CARD . " WHERE id= " . $card_id);
        if (!$card) {
            Card::openPayRedis($card_id);
            $saveCartErr = array('cart_id' => 0,'transaction_id' => '','status_code_id'=>511,'app_id' => $agent_site);
            return System::apiError((isset(System::$data['status'][511]) ? System::$data['status'][511]['title'] : 'Không tìm thấy thông tin thẻ.'), ['status'=>511],$saveCartErr);
            #return System::apiError('Không tìm thấy  thông tin thẻ.', false);
        }
        if($site['id'] == JwtService::APP_ID_URBOX && $card['wallet_id'] != $wallet_id){
            Card::openPayRedis($card_id);
            $saveCartErr = array('cart_id' => 0,'transaction_id' => '','status_code_id'=>511,'app_id' => $agent_site);
            return System::apiError((isset(System::$data['status'][511]) ? System::$data['status'][511]['title'] : 'Không tìm thấy thông tin thẻ.'), ['status'=>511],$saveCartErr);
        }
        if ($card['valid_time'] >0 && $card['valid_time'] > TIME_NOW) {
            Card::openPayRedis($card_id);
            $saveCartErr = array('cart_id' => 0,'transaction_id' => '','status_code_id'=>516,'app_id' => $agent_site);
            return System::apiError((isset(System::$data['status'][516]) ? System::$data['status'][516]['title'] : 'Chưa tời hạn sử dụng thẻ.'), ['status'=>516],$saveCartErr);
        }
        if ($card['expired_time'] >0 && $card['expired_time'] <= TIME_NOW) {
            Card::openPayRedis($card_id);
            $saveCartErr = array('cart_id' => 0,'transaction_id' => '','status_code_id'=>515,'app_id' => $agent_site);
            return System::apiError((isset(System::$data['status'][515]) ? System::$data['status'][515]['title'] : 'Đã hết hạn sử dụng.'), ['status'=>515],$saveCartErr);
        }
        /*if ($card['money'] <= 0) {
            return System::apiError('Tài khoản không còn tiền.', false);
        }*/
        #Kiem tra the neu type= 5 và 8 chi dc phep cho tieu 1 lan
        if ($card['type'] == 5 || $card['type'] == 8) {
            $hold = DB::count(T_HOLD_CODE,"is_hold = 2 and status = 2 and code = '".System::encrypt("UB".$card['number'])."'");
            if($hold>0){
                Card::openPayRedis($card_id);
                $saveCartErr = array('cart_id' => 0,'transaction_id' => '','status_code_id'=>511,'app_id' => $agent_site);
                return System::apiError((isset(System::$data['status'][514]) ? System::$data['status'][514]['title'] : 'Thẻ thanh toán không hợp lệ.'), ['status'=>514],$saveCartErr);
            }
            $cardTransaction = DB::count(T_CARD_TRANSACTION,'status=2 AND type=2 and card_id='.$card['id']);
            if($cardTransaction>0){
                Card::openPayRedis($card_id);
                $saveCartErr = array('cart_id' => 0,'transaction_id' => '','status_code_id'=>511,'app_id' => $agent_site);
                return System::apiError((isset(System::$data['status'][514]) ? System::$data['status'][514]['title'] : 'Thẻ thanh toán không hợp lệ.'), ['status'=>514],$saveCartErr);
            }
        }
        #Thông tin cho TOPUP
        $firstname = Url::getParam('firstname','');
        $lastname = Url::getParam('lastname','');
        $member_id = Url::getParam('member_id','');
        #END
        #Lay thong tin nguoi dang nhap
        $emailnguoigui = trim(Url::getParam('emailnguoigui', ''));
        $nguoigui = trim(Url::getParam('nguoigui', ''));
        $phonenguoigui = trim(Url::getParam('phonenguoigui', ''));

        #Lay thong tin don hang

        $ttmessage = trim(Url::getParam('ttmessage', ''));
        $delivery_note = trim(Url::getParam('delivery_note', ''));
        $ttemail = trim(Url::getParam('ttemail', ''));
        $ttphone = trim(Url::getParam('ttphone', ''));
        $ttfullname = trim(Url::getParam('ttfullname', ''));
        $ttcoupon = trim(Url::getParam('ttcoupon', ''));
        $ttaddress = trim(Url::getParam('ttaddress', ''));
        $ttDateSend = trim(Url::getParam('ttDateSend', ''));
        $ip = Url::getParam('ip', '');

        $address_id = Url::getParamInt('address_id', 0);
        $isSendSms = Url::getParamInt('isSendSms', 0);

        $dataBuy = trim(html_entity_decode(Url::getParam('dataBuy', '')));

        $thiep_id = Url::getParamInt('thiep_id', 0);

        if (in_array($ttphone, Cart::$arrPhone) || in_array($phonenguoigui, Cart::$arrPhone)) {
            Card::openPayRedis($card_id);
            LogsApi::insert('Hack', FunctionLib::ip(), 'Khách ' . $ttphone . ' - ' . $phonenguoigui . ' đặt mua ở card:' . $card_id . ' lúc: ' . TIME_NOW . json_encode($dataBuy));
            return System::apiError('Hiện kho quà đang hết vui lòng quay lại sau.', false);
        }

        if($card['wallet_id']>0 && $ttphone==''){
            Card::openPayRedis($card_id);
            $saveCartErr = array('cart_id' => 0,'transaction_id' => '','status_code_id'=>610,'app_id' => $agent_site);
            return System::apiError('Không tìm thấy số điện thoại', ['status'=>610],$saveCartErr);
        }
        if ($ttphone != '' && !FunctionLib::is_phone($ttphone)) {
            Card::openPayRedis($card_id);
            $saveCartErr = array('cart_id' => 0,'transaction_id' => '','status_code_id'=>610,'app_id' => $agent_site);
            return System::apiError(t('Không tìm thấy số điện thoại'), ['status'=>610],$saveCartErr);
        }

        #LOG MUA HÀNG 11-5-2022
        $giftsLog = array();
        $titleGift = '';
        $contentLog = array(
            "createdAt" => date("Y-m-d\TH:i:s\Z", TIME_NOW),
            "legacyCustomerId" => 0,
            "cart_id" => 0,
            "title" => "",
            "amount" => 0,
            "money_ship" => 0,
            "money_fee" => 0,
            "card" => [
                "number" => substr($card['number'],12),
                "logo" => "",
                "money" => 0,
                "title" => DB::fetch("SELECT title FROM ".T_APP." WHERE id=".$card['app_id'],"title"),
            ]
        );
        #Lay thong tin don hang
        $arrCart = json_decode(StringLib::post_db_parse_html($dataBuy), true);
        $arrCartDetail = array();
        if (empty($arrCart)) {
            Card::openPayRedis($card_id);
            $saveCartErr = array('cart_id' => 0,'transaction_id' => '','status_code_id'=>221,'app_id' => $agent_site);
            $message_err = (isset(System::$data['status'][221]) ? System::$data['status'][221]['title'] : 'Không tìm thấy sản phẩm mua.');
            Sentry::cart(9,223,$agent_site,'',$message_err,array('app_name'=>$site['title'],'dataBuy'=>$dataBuy,'phone'=>$ttphone,'email'=>$ttemail,'ip_address'=>$ip));
            return System::apiError($message_err, ['status'=>221],$saveCartErr);
            #return System::apiError('Không tìm thấy sản phẩm mua.', false);
        }
        $send_time = 0;
        if ($ttDateSend != '') {
            $arrTime = array(0 => 8, 1 => 30);
            $arrDate = array();
            if (isset($arrDateTime[0])) {
                $arrDate = explode('/', $ttDateSend);
            }

            if (!empty($arrDate) && !empty($arrTime)) {
                $send_time = mktime((int)$arrTime[0], (int)$arrTime[1], 0, (int)$arrDate[1], (int)$arrDate[0], (int)$arrDate[2]);
            }
        }

        $ttemail = Customer::fixEmail($ttemail);

        $arrGiftId = array();
        if (!empty($arrCart)) {
            foreach ($arrCart as $key => $item) {
                if ($item['quantity'] <= 0) {
                    Card::openPayRedis($card_id);
                    $saveCartErr = array('cart_id' => 0,'transaction_id' => '','status_code_id'=>404,'app_id' => $agent_site);
                    return System::apiError((isset(System::$data['status'][404]) ? System::$data['status'][404]['title'] : 'Số lượng phải lớn hơn 0'), ['status'=>404],$saveCartErr);
                    #return System::apiError('Số lượng phải lớn hơn 0', false);
                } else {
                    if (FunctionLib::check_number($item['priceId'])) {
                        $arrCartDetail[$item['priceId']] = array('quantity' => $item['quantity']);
                        if (!isset($item['id']) || (int)$item['id'] == 0) {

                            $giftID = DB::fetch("SELECT gift_id from " . T_GIFT_DETAIL . " WHERE id=" . ((int)$item['priceId']) . " LIMIT  1");
                            $arrGiftId[$giftID['gift_id']] = $giftID['gift_id'];
                            $arrCart[$key]['id'] = $giftID['gift_id'];
                        } else $arrGiftId[$item['id']] = $item['id'];
                    } else {
                        Card::openPayRedis($card_id);
                        $saveCartErr = array('cart_id' => 0,'transaction_id' => '','status_code_id'=>222,'app_id' => $agent_site);
                        $message_err = (isset(System::$data['status'][222]) ? System::$data['status'][222]['title'] : 'Không tìm thấy sản phẩm');
                        Sentry::cart(9,222,$agent_site,'',$message_err,array('app_name'=>$site['title'],'dataBuy'=>$dataBuy,'phone'=>$ttphone,'email'=>$ttemail,'ip_address'=>$ip));
                        return System::apiError($message_err, ['status'=>222],$saveCartErr);
                        #return System::apiError('Không tìm thấy sản phẩm', false);
                    }
                }
            }
            if (!empty($arrGiftId)) {
                $gift_check = DB::count(T_GIFT, 'status IN(1,2) AND id IN(' . implode(',', $arrGiftId) . ')');
                if ($gift_check != count($arrGiftId)) {
                    Card::openPayRedis($card_id);
                    $message_err = (isset(System::$data['status'][223]) ? System::$data['status'][223]['title'] : '1 trong số quà tặng bạn đặt mua đã hết hạn, bạn hãy chọn lại quà khác.').implode(',', $arrGiftId);
                    Sentry::cart(1,223,$agent_site,'',$message_err,array('app_name'=>$site['title'],'dataBuy'=>$dataBuy,'phone'=>$ttphone,'email'=>$ttemail,'ip_address'=>$ip));
                    $saveCartErr = array('cart_id' => 0,'transaction_id' => '','status_code_id'=>223,'app_id' => $agent_site);
                    return System::apiError($message_err, ['status'=>223],$saveCartErr);
                    #return System::apiError('1 trong số quà tặng bạn đặt mua đã hết hạn, bạn hãy chọn lại quà khác.', false);
                }
            } else {
                Card::openPayRedis($card_id);
                $saveCartErr = array('cart_id' => 0,'transaction_id' => '','status_code_id'=>224,'app_id' => $agent_site);
                $message_err = (isset(System::$data['status'][224]) ? System::$data['status'][224]['title'] : 'Không tìm thấy quà tặng');
                Sentry::cart(9,224,$agent_site,'',$message_err,array('app_name'=>$site['title'],'dataBuy'=>$dataBuy,'phone'=>$ttphone,'email'=>$ttemail,'ip_address'=>$ip));
                return System::apiError($message_err, ['status'=>224],$saveCartErr);
                #return System::apiError('Không tìm thấy quà tặng', false);
            }
        } else {
            Card::openPayRedis($card_id);
            $saveCartErr = array('cart_id' => 0,'transaction_id' => '','status_code_id'=>405,'app_id' => $agent_site);
            $message_err = (isset(System::$data['status'][405]) ? System::$data['status'][405]['title'] : 'Bạn vui lòng chọn quà muốn tặng trước.');
            Sentry::cart(9,405,$agent_site,'',$message_err,array('app_name'=>$site['title'],'dataBuy'=>$dataBuy,'phone'=>$ttphone,'email'=>$ttemail,'ip_address'=>$ip));
            return System::apiError($message_err, ['status'=>405],$saveCartErr);
            #return System::apiError('Bạn vui lòng chọn quà muốn tặng trước.', false);
        }
        #Kiem tra qua ko co trong Bo Qua khong cho mua
        if($card['gift_id']>0 && !empty($arrCartDetail)){
            $giftSet = Gift::giftSetbyGiftID($card['gift_id']);
            $errGiftSet = 0;
            if(!empty($giftSet)){
                foreach ($arrCartDetail as $k => $giftDe){
                    if(!isset($giftSet[$k])){
                        $errGiftSet = 1;
                    }
                }
            }else{
                $errGiftSet = 1;
            }
            if($errGiftSet == 1){
                Card::openPayRedis($card_id);
                $message_err = isset(System::$data['status'][226]) ? System::$data['status'][226]['title'] : 'Quà tặng không nằm trong chương trình';
                Sentry::cart(2,226,$agent_site,'',$message_err,array('app_name'=>$site['title'],'dataBuy'=>$dataBuy,'phone'=>$ttphone,'email'=>$ttemail,'ip_address'=>$ip));
                $saveCartErr = array('cart_id' => 0,'transaction_id' => '','status_code_id'=>226,'app_id' => $agent_site);
                return System::apiError($message_err, ['status'=>226],$saveCartErr);
                #return System::apiError('Voucher không được áp dụng trên thẻ');
            }
        }
        #END

        if ($ttemail != '' && !FunctionLib::is_valid_email($ttemail)) {
            Card::openPayRedis($card_id);
            $saveCartErr = array('cart_id' => 0,'transaction_id' => '','status_code_id'=>304,'app_id' => $agent_site);
            return System::apiError((isset(System::$data['status'][304]) ? System::$data['status'][304]['title'] : 'Email không đúng định dạng'), ['status'=>304],$saveCartErr);
            #return System::apiError('Email không đúng định dạng', false);
        }

        if ($ttcoupon != '' && !Coupon::isValid($ttcoupon, $arrGiftId)) {
            Card::openPayRedis($card_id);
            $saveCartErr = array('cart_id' => 0,'transaction_id' => '','status_code_id'=>406,'app_id' => $agent_site);
            return System::apiError((isset(System::$data['status'][406]) ? System::$data['status'][406]['title'] : 'Mã khuyến mại không đúng'), ['status'=>406],$saveCartErr);
            #return System::apiError('Mã khuyến mại không đúng', false);
        }
        $detailGifts = array();
        if (!empty($arrCartDetail)) {
            $detailGifts = DB::fetch_all("SELECT id,code_quantity,justGetOrder,gift_id,valuex FROM " . T_GIFT_DETAIL . " WHERE id IN(".implode(',', array_keys($arrCartDetail)).") ORDER BY position ASC");
            foreach ($detailGifts as $k => $quan) {
                if (in_array($quan['gift_id'], $arrGiftId)) {
                    if ($quan['code_quantity'] < $arrCartDetail[$quan['id']]['quantity']) {
                        $gift = DB::fetch("SELECT id FROM ".T_GIFT." WHERE id=".$quan['gift_id']);
                        Card::openPayRedis($card_id);
                        $message_err = (isset(System::$data['status'][407]) ? System::$data['status'][407]['title'] : 'Số lượng sản phẩm không đủ');
                        Sentry::cart(1,407,$agent_site,'',$message_err,array('app_name'=>$site['title'],'dataBuy'=>$dataBuy,'phone'=>$ttphone,'email'=>$ttemail,'ip_address'=>$ip));
                        $saveCartErr = array('cart_id' => 0,'transaction_id' => '','status_code_id'=>407,'app_id' => $agent_site);
                        return System::apiError($message_err, ['status'=>407,'gift' => $gift],$saveCartErr);
                        #return System::apiError('Số lượng sản phẩm không đủ', array('gift' => $gift));
                    }
                } else {
                    Card::openPayRedis($card_id);
                    $message_err = (isset(System::$data['status'][223]) ? System::$data['status'][223]['title'] : '1 trong số quà tặng bạn đặt mua đã hết hạn, bạn hãy chọn lại quà khác.');
                    Sentry::cart(1,223,$agent_site,'',$message_err,array('app_name'=>$site['title'],'dataBuy'=>$dataBuy,'phone'=>$ttphone,'email'=>$ttemail,'ip_address'=>$ip));
                    $saveCartErr = array('cart_id' => 0,'transaction_id' => '','status_code_id'=>223,'app_id' => $agent_site);
                    return System::apiError($message_err, ['status'=>223],$saveCartErr);
                    #return System::apiError('1 trong số quà tặng bạn đặt mua đã hết hạn, bạn hãy chọn lại quà khác.', false);
                }
            }
        }

        $cart = array(
            'from' => array('name' => $nguoigui, 'phone' => $phonenguoigui, 'email' => $emailnguoigui, 'message' => $ttmessage, 'thiep_id' => $thiep_id),
            'to' => array('name' => $ttfullname, 'phone' => $ttphone, 'email' => $ttemail),
            'items' => $arrCart,
            'ip' => $ip,
            'coupon' => $ttcoupon,
            'address' => $ttaddress,
            'send_time' => $send_time,
            'site_id' => ($card['app_id']!=$agent_site)?$card['app_id']:$agent_site,
            'card_id' => $card_id,
            'wallet_id' => $card['wallet_id'],
            'address_id' => $address_id,
            'delivery_note' => $delivery_note,
            'channel' => $channel,
            'payment_type' => PAY_CARD
        );

        $msg = '';
        $arrCartFinal = Cart::createOrder($cart, $msg);
        if ($msg == '') {
            $msg = 'Hệ thống hiện tại không tạo được đơn hàng.';
        }
        if (!empty($arrCartFinal)) {
            if ($isSendSms == 0) {
                DB::update(T_CART, array('delivery' => 1, 'smsthankiu' => 1,'transaction_id'=>$transaction_id), 'id=' . $arrCartFinal['id']);
                DB::update(T_CART_DETAIL, array('delivery' => 1, 'sendSms' => 1), 'cart_id=' . $arrCartFinal['id']);
                $arrLog = array('cart_id' => $arrCartFinal['id'], 'user_id' => 0, 'action_from' => 5, 'action_to' => 2, 'action_time' => TIME_NOW, 'action_type' => 8);
                Cart::insertLogCart($arrLog);
            }
            $checkPay = false;
            if($card['status']!=IS_ON) {
                if ($card['status'] == 1 && (in_array($agent_site, array(95, 126, 103)))) {
                    #Tam thoi cho CHI TIEU LUON
                    $checkPay = true;
                } else {
                    Card::openPayRedis($card_id);
                    $message_err = (isset(System::$data['status'][513]) ? System::$data['status'][513]['title'] : ' bạn chưa sẵn sàng chi tiêu');
                    Sentry::cart(3, 513, $agent_site, '', $message_err, array('app_name' => $site['title'], 'dataBuy' => $dataBuy, 'phone' => $ttphone, 'email' => $ttemail, 'ip_address' => $ip));
                    $saveCartErr = array('cart_id' => $arrCartFinal['id'], 'transaction_id' => '', 'status_code_id' => 513, 'app_id' => $agent_site);
                    return System::apiError($message_err, ['status' => 513], $saveCartErr);
                    #return System::apiError('Thẻ bạn chưa sẵn sàng chi tiêu', false);
                }
            }else{
                $checkPay = true;
            }
           if($checkPay == true){
                #Thanh toan qua THẺ
                $isMail = ($agent_site==50)?1:2;
                if (!empty($card) && $card['money'] >= $arrCartFinal['money_total'] && Card::checkHashMoney($card,$isMail)) {
                    $cart = Cart::getCart($arrCartFinal['id'],false);
                    $detail_cart = array();
                    if (isset($cart['detail']) && $cart['detail']) {
                        #Ngay 5-6-2019 Doi lại luong xuat code truoc khi thanh toan
                        $codeEx = array();
                        $giftIdErr = 0;
                        foreach ($cart['detail'] as $detail) {
                            GiftCode::assignCode($detail['id'], $code);
                            $code = System::decrypt($code);
                            if ($code != '') {
                                $codeEx[$code] = array(
                                    'code' => $code,
                                    'brand_id' => $detail['brand_id']
                                );
                            } else {
                                $giftIdErr = $detail['gift_detail_id'];
                                break;
                            }
                            $detail_cart[] = array('id' => $detail['id']);
                            if(isset($giftsLog[$detail['gift_detail_id']])){
                                $giftsLog[$detail['gift_detail_id']]['quantity'] = $giftsLog[$detail['gift_detail_id']]['quantity']+1;
                            }else{
                                $giftsLog[$detail['gift_detail_id']] = array(
                                    'id' => $detail['gift_detail_id'],
                                    'phone' => $ttphone,
                                    'cart_detail_id' => $detail['id'],
                                    'title' => $cart['gifts'][$detail['gift_id']]['detail'][$detail['gift_detail_id']]['title'],
                                    'quantity' => 1,
                                    'type' => $cart['gifts'][$detail['gift_id']]['detail'][$detail['gift_detail_id']]['type'],
                                    'pointu_id' => $cart['gifts'][$detail['gift_id']]['detail'][$detail['gift_detail_id']]['pointu_id'],
                                    'price' => $cart['gifts'][$detail['gift_id']]['detail'][$detail['gift_detail_id']]['price'],
                                    'images' => $cart['gifts'][$detail['gift_id']]['detail'][$detail['gift_detail_id']]['avatar'],
                                    'brand' => [
                                        "id"=> $detail['brand_id'],
                                        "title" => $cart['brand'][$detail['brand_id']]['title'],
                                        "title_en" => $cart['brand'][$detail['brand_id']]['title_en'],
                                        "online" => $cart['brand'][$detail['brand_id']]['online']
                                    ]
                                );
                            }
                        }

                        if (count($codeEx) == count($cart['detail'])) {
                            if(Card::endPaybyRedis($card, $arrCartFinal['money_total'], array('app_id' => $agent_site, 'cart_id' => $arrCartFinal['id']))){
                                Cart::payed($arrCartFinal['id'],$cart);
                                Cart::codeComboExpired($cart);
                            }else{
                                Card::openPayRedis($card_id);
                                GiftCode::removeCodeOutCart($arrCartFinal['id'],$arrCartFinal);
                                $message_err = (isset(System::$data['status'][306]) ? System::$data['status'][306]['title'] : 'Hệ thống khách hàng không đủ tiền');
                                Sentry::cart(3,306,$agent_site,'',$message_err,array('app_name'=>$site['title'],'dataBuy'=>$dataBuy,'phone'=>$ttphone,'email'=>$ttemail,'ip_address'=>$ip));
                                $saveCartErr = array('cart_id' => $arrCartFinal['id'],'transaction_id' => '','status_code_id'=>306,'app_id' => $agent_site);
                                return System::apiError($message_err, ['status'=>306],$saveCartErr);
                                #return System::apiError('Hệ thống khách hàng không đủ tiền', false);
                            }
                        } else {
                            #Cap nhat bo het code ra khỏi dơn hang
                            GiftCode::removeCodeOutCart($arrCartFinal['id'],$arrCartFinal);
                            Card::openPayRedis($card_id);
                            $message_err = ('Sản phẩm ') . DB::fetch("SELECT title FROM " . T_GIFT_DETAIL . " WHERE id=" . $giftIdErr, 'title') . (" đang hết, vui lòng bỏ sản phẩm ra khỏi giỏ hàng.");
                            Sentry::cart(1,225,$agent_site,'',$message_err,array('app_name'=>$site['title'],'dataBuy'=>$dataBuy,'phone'=>$ttphone,'email'=>$ttemail,'ip_address'=>$ip));
                            $saveCartErr = array('cart_id' => $arrCartFinal['id'],'transaction_id' => '','status_code_id'=>225,'app_id' => $agent_site);
                            return System::apiError($message_err, ['status'=>225],$saveCartErr);
                            #return System::apiError('Sản phẩm ' . DB::fetch("SELECT title FROM " . T_GIFT_DETAIL . " WHERE id=" . $giftIdErr, 'title') . " đang hết, vui lòng bỏ sản phẩm ra khỏi giỏ hàng.", false);
                        }
                    }
                    $info  = array(
                        'firstname' => $firstname,
                        'lastname' => $lastname,
                        'full_name' => $ttfullname,
                        'member_id' => $member_id
                    );
                    $parseCart = Cart::parseCart($cart,$ttphone,$info,$arrCallback);
                    if($card['wallet_id'] >0){
                        GiftCode::clearCacheWallet($card['wallet_id'],'payment-code');
                    }
                    $contentLog['legacyCustomerId'] = ($card['wallet_id']>0?DB::fetch("SELECT customer_id FROM ".T_WALLET." WHERE id=".$card['wallet_id'],"customer_id"):0);
                    $contentLog['params']['gifts'] = array_values($giftsLog);
                    if(!empty($giftsLog)){
                        foreach ($giftsLog as $gL){
                            if($titleGift==''){
                                $titleGift = $gL['title'].' x'. $gL['quantity'];
                            }else{
                                $titleGift .= ' \n '.$gL['title'].' x'. $gL['quantity'];
                            }
                        }
                    }
                    $contentLog['params']['firstname'] = $firstname;
                    $contentLog['params']['lastname'] = $lastname;
                    $contentLog['params']['member_id'] = $member_id;
                    $contentLog['title'] = $titleGift;
                    $contentLog['cart_id'] = $arrCartFinal['id'];
                    $contentLog['amount'] = $cart['money_total'];
                    $contentLog['money_ship'] = $cart['money_ship'];
                    $contentLog['money_fee'] = $cart['money_fee'];
                    $contentLog['card']['money'] = ($card['money'] - $cart['money_total']);
                    Urcard::transaction_history('giftcard','card-payment',$contentLog);
                    return System::apiSuccess(
                        $parseCart
                    );

                } else {
                    Card::openPayRedis($card_id);
                    $message_err = (isset(System::$data['status'][306]) ? System::$data['status'][306]['title'] : 'Hệ thống khách hàng không đủ tiền');
                    Sentry::cart(3,306,$agent_site,'',$message_err,array('app_name'=>$site['title'],'dataBuy'=>$dataBuy,'phone'=>$ttphone,'email'=>$ttemail,'ip_address'=>$ip));
                    $saveCartErr = array('cart_id' => $arrCartFinal['id'],'transaction_id' => '','status_code_id'=>306,'app_id' => $agent_site);
                    return System::apiError($message_err, ['status'=>306],$saveCartErr);
                    #return System::apiError('Hệ thống khách hàng không đủ tiền', false);
                }
            }
        }
        Card::openPayRedis($card_id);
        $saveCartErr = array('cart_id' => 0,'transaction_id' => '','status_code_id'=>408,'app_id' => $agent_site);
        return System::apiError((isset(System::$data['status'][408]) ? System::$data['status'][408]['title'] : $msg), ['status'=>408],$saveCartErr);
        #return System::apiError($msg, false);


    }

    function topupUpdateByTransaction()
    {

        $access_urbox = Url::getParam('app_secret', '');
        $agent_site = Url::getParamInt('app_id', 0);
        #Thông tin cho TOPUP
        $firstname = Url::getParam('firstname', '');
        $lastname = Url::getParam('lastname', '');
        $member_id = Url::getParam('member_id', '');
        #END
        if ($firstname == '' || $lastname == '' || $member_id == '') {
            return System::apiError('Không tìm thấy thông tin cập nhật', false);
        }
        if (!Site::check_access_urbox($agent_site, $access_urbox)) {
            return System::apiError('Access Urbox không đúng.', false);
        }

        $transaction_id = Url::getParam('transaction_id', '');
        if ($transaction_id == '') {
            return System::apiError('Không tìm thấy transaction_id.', false);
        }

        $cart = DB::fetch("SELECT id FROM " . T_CART . " WHERE site_id=" . $agent_site . " AND status=2 AND pay_status=2 AND transaction_id='" . $transaction_id . "'");
        if (empty($cart)) {
            return System::apiError('Không tìm thấy đơn hàng', false);
        }
        $re = DB::query("SELECT id,gift_id FROM " . T_CART_DETAIL . " WHERE status=2 AND pay_status=2 AND cart_id = " . $cart['id']);
        if (!$re) {
            return System::apiError('Không tìm thấy đơn hàng', false);
        }
        $orderTOPUP = DB::query("SELECT * FROM " . T_ORDER_TOPUP . " WHERE status=2 AND process=4 AND cart_id=" . $cart['id']);
        if (empty($orderTOPUP)) {
            return System::apiError('Không tìm thấy giao dịch TOPUP nào', false);
        }
        DB::update(T_ORDER_TOPUP, array('process' => 2, 'sent_time' => 0, 'firstname' => $firstname, 'lastname' => $lastname, 'member_id' => $member_id), 'cart_id=' . $cart['id']);
        return System::apiSuccess();

    }

    function getByTransaction()
    {
        $access_urbox = Url::getParam('app_secret', '');
        $agent_site = Url::getParamInt('app_id', 0);
        if(!System::acceptApp($agent_site)){
            return System::apiError('Access Urbox không đúng.', ['status'=>211]);
        }
        if (!Site::check_access_urbox($agent_site, $access_urbox)) {
            return System::apiError('Access Urbox không đúng.', ['status'=>211]);
        }

        $cart_id = Url::getParamInt('cart_id', 0);
        $transaction_id = Url::getParam('transaction_id', 0);
        $lang = Url::getParam('lang', 'vi');
        $shorten = Url::getParamInt('shorten', 0);
        Language::$activeLang = $lang;
        Language::loadWordsFromLang(Language::$activeLang);
        $cart = array();
        $linkshorten = array();
        if ($cart_id > 0) {
            $cart = Cart::get($cart_id);

        } else {
            if ($transaction_id != '') {
                $cart = DB::fetch("SELECT * FROM " . T_CART . " WHERE status=2 AND site_id=".$agent_site." AND transaction_id='" . $transaction_id . "' ORDER BY id desc ");
            }
        }
        if ($cart) {
            if ($cart['site_id'] != $agent_site) {
                return System::apiError('Không tìm thấy dữ liệu.', ['status'=>409]);
            }
            if ($cart['pay_status'] == IS_YES) {
                $key = md5($cart['id'] . $cart['customer_id'] . $cart['receiver_id'] . $cart['email'] . $cart['phone'] . 'KIEMTRAMONQUACHUAN');
                $linkCart = LINK_API_URBOX.'cart/'.$key.'.html?cart_no='.$cart['id'];
                $linkshorten[] = $linkCart;
                $addressArr = array();
                $address = '';
                if($cart['address_id'] > 0){
                    $province ='';
                    $district ='';
                    $ward ='';
                    $addressArr = DB::fetch("SELECT * FROM ".T_ADDRESS." WHERE id=".$cart['address_id']);
                    if(!empty($addressArr)){
                        $province = DB::fetch("SELECT title FROM ".T_PROVINCE." WHERE id=".$addressArr['city_id'],'title');
                        $district = DB::fetch("SELECT title FROM ".T_DISTRICT." WHERE id=".$addressArr['district_id'],'title');
                        $ward = DB::fetch("SELECT title FROM ".T_WARD." WHERE id=".$addressArr['ward_id'],'title');
                        $address = $addressArr['number'].', '.$ward.', '.$district.', '.$province;
                    }

                }
                $giftReceiver = DB::fetch("SELECT email,phone FROM " . T_GIFT_RECEIVER . " WHERE id=" . $cart['receiver_id']);
                $giftReceiver['address'] = $address;
                $linkCombo = '';
                if($cart['combo_id'] > 0 ){
                    $linkCombo = DOMAIN_LOYAL . 'combo/'. $cart['token'];
                    $linkshorten[] = $linkCombo;
                }
                $arr_return = array(
                    'id' => $cart['id'],
                    'campaign_code' => $cart['campaign_code'],
                    'linkCart' => $linkCart,
                    'linkCombo' => $linkCombo,
                    'money_ship' => $cart['money_ship'],
                    'money_total' => $cart['money_total'],
                    'created' => FunctionLib::dateFormat($cart['created'], 'd/m/y'),
                    'pay_time' => $cart['pay_time'] > 0 ? FunctionLib::dateFormat($cart['pay_time'], 'd/m/y') : '--',
                    'pay_status' => t($cart['pay_status']==1?'Chưa thanh toán':'Đã thanh toán'),
                    'pay_status_code' => $cart['pay_status']==1?1:2,
                    'customer' => DB::fetch("SELECT email,phone FROM " . T_CUSTOMER . " WHERE id=" . $cart['customer_id']),
                    'receiver' => $giftReceiver,
                    'item_quantity' => 0,
                    'detail' => array(),
                );
                $re = DB::query("SELECT * FROM " . T_CART_DETAIL . " WHERE cart_id = " . $cart['id']);
                if ($re) {
                    $detail = array();
                    $arrTopup = array();
                    $reT = DB::query("SELECT id,member_id,firstname,lastname,process,note,cart_detail_id FROM " . T_ORDER_TOPUP . " WHERE cart_id =" . $cart['id']);
                    if ($reT) {
                        while ($rowT = @mysql_fetch_assoc($reT)) {
                            $arrTopup[$rowT['cart_detail_id']] = $rowT;
                        }
                    }
                    $data = array();
                    $idGift = array();
                    while ($rcc = @mysql_fetch_assoc($re)) {
                        $data[$rcc['id']] = $rcc;
                        $idGift[$rcc['gift_id']] = $rcc['gift_id'];
                    }
                    $gifts = array();
                    $gift_codes = array();
                    $deliveryDetails = array();
                    if(!empty($idGift)){
                        $gifts = DB::fetch_all("SELECT id,isPhysical,showbarcode,type,pointu_id,type FROM ".T_GIFT." WHERE id In (".implode(',',$idGift).")");
                    }
                    if(!empty($data)){
                        $regc = DB::query("SELECT id,code,expired,cart_detail_id,active FROM ".T_GIFT_CODE." WHERE cart_detail_id In (".implode(',',array_keys($data)).")");
                        if($regc){
                            while ($rc = @mysql_fetch_assoc($regc)) {
                                $gift_codes[$rc['cart_detail_id']] = $rc;
                            }
                        }
                        $regd = DB::query("SELECT id,cart_detail_id,process,agency_code,finish_time FROM ".T_DELIVERY_DETAIL." WHERE cart_detail_id In (".implode(',',array_keys($data)).")");
                        if($regd){
                            while ($rd = @mysql_fetch_assoc($regd)) {
                                $deliveryDetails[$rd['cart_detail_id']] = $rd;
                            }
                        }
                    }
                    foreach ($data as $r){
                        $expired = '';
                        $code = '';
                        $link = LINK_API_URBOX . 'nhan-qua/' . System::decrypt($r['receive_code']) . '.html';
                        $linkshorten[] = $link;
                        $gift_code = isset($gift_codes[$r['id']])?$gift_codes[$r['id']]:(DB::fetch("SELECT id,code,expired,cart_detail_id,active FROM ".T_GIFT_CODE." WHERE cart_detail_id=".$r['id']));
                        if($gift_code){
                            $code = System::decrypt($gift_code['code']);
                            $expired = $gift_code['expired'] == 0 ? t('Vô hạn') : FunctionLib::dateFormat($gift_code['expired'], 'd/m/Y');
                        }
                        $deliveryDetail = array();
                        if($r['delivery_required'] == 2){
                            $deliveryDetail = isset($deliveryDetails[$r['id']])?$deliveryDetails[$r['id']]:(DB::fetch("SELECT id,cart_detail_id,process,agency_code,finish_time FROM ".T_DELIVERY_DETAIL." WHERE cart_detail_id=".$r['id']));
                        }
                        $topup = array();
                        if (!empty($arrTopup) && isset($arrTopup[$r['id']])) {
                            $topup = [
                                'member_id' => $arrTopup[$r['id']]['member_id'],
                                'firstname' => $arrTopup[$r['id']]['firstname'],
                                'lastname' => $arrTopup[$r['id']]['lastname'],
                                'process' => $arrTopup[$r['id']]['process'],
                                'usage_process' => $arrTopup[$r['id']]['process'] == 1 ? t('Chờ duyệt') : (($arrTopup[$r['id']]['process'] == 2) ? t('Chờ nạp') : ($arrTopup[$r['id']]['process'] == 3 ? t('Nạp dặm thành công') : t('Nạp dặm thất bại'))),
                                'note' => ($arrTopup[$r['id']]['process'] == 2) ? 'Waiting for topup' : $arrTopup[$r['id']]['note'],
                            ];
                        }
                        $temp  = array(
                            'using_time' => $r['using_time']>0?FunctionLib::dateFormat($r['using_time'],'d/m/Y H:i'):'',
                            'finish_time' => isset($deliveryDetail['finish_time'])?$deliveryDetail['finish_time']:"0",
                            'link' => $r['delivery_required'] == 1?$link:'',
                            'delivery' => t($r['delivery_required'] == 2? (empty($deliveryDetail)?'Đang chờ xử lý':($deliveryDetail['process']==1?'Đang chờ xử lý':($deliveryDetail['process']==2?'Đã chuyển hàng':($deliveryDetail['process']==3?'Thành công':($deliveryDetail['process']==4?'Vận chuyển trả hàng':'Chờ có hàng'))))): ($r['delivery'] == 3 ? 'Đã sử dụng' : 'Chưa sử dụng')),
                            'deliveryCode' => $r['delivery_required'] == 2? (empty($deliveryDetail)?6:($deliveryDetail['process']==1?6:($deliveryDetail['process']==2?7:($deliveryDetail['process']==3?8:($deliveryDetail['process']==4?9:10))))): ($r['delivery'] == 3 ? 2 : 1),
                            'delivery_tracking' =>  (($r['delivery_required'] == 2 && isset($deliveryDetail['agency_code'])) ? $deliveryDetail['agency_code']:''),
                            'estimateDelivery' => ($r['delivery_required'] == 2 ? t('Dự kiến giao hàng từ 3 - 5 ngày làm việc.'):''),
                            'delivery_note' => $r['delivery_note'],
                            'code' => $r['delivery_required'] == 1 ? $code : '',
                            'topup' => $topup,
                            'gift_id' => $r['gift_id'],
                            'priceId' => $r['gift_detail_id'],
                            'expired' => $r['delivery_required'] == 1?$expired:'',
                            'id' => $r['id']
                        );
                        if($gift_code['active']==1 && $gift_code['expired']>0 && $gift_code['expired']<TIME_NOW ){
                            $temp['delivery'] = t('Hết hạn');
                            $temp['deliveryCode'] = 4;
                        }
                        if($r['delivery']>=4000 && $r['delivery']<=5000){
                            $temp['delivery'] = t('Hủy đơn hàng');
                            $temp['deliveryCode'] = 11;
                        }
                        if(isset($gifts[$r['gift_id']])){
                            if($gifts[$r['gift_id']]['type'] == 14){#Loai qua quay so
                                if(isset(System::$data['point_unit'][$gift['pointu_id']])){
                                    $temp['code'] = '';
                                    $temp['link'] = System::$data['point_unit'][$gifts[$r['gift_id']]['pointu_id']]['url'] . $cart['site_user_id'];
                                    $linkshorten[] = $temp['link'];
                                }
                            }
                            if($gifts[$r['gift_id']]['type'] == 4){
                                $temp['link'] = DOMAIN_LOYAL . 'card/' . System::decrypt($gift_code['code']);
                                $temp['code'] = '';
                                $temp['card_id'] = DB::fetch("SELECT id FROM ".T_CARD." WHERE token='".$gift_code['code']."'");
                                $linkshorten[] = $temp['link'];
                            }
                            if($gifts[$r['gift_id']]['type'] == 19 || $gifts[$r['gift_id']]['type'] == 22){
                                $temp['link'] = DOMAIN_URCARD_LINK . System::decrypt($r['receive_code']);
                                $arr_return['id'] = $cart['ncart_id'];
                            }
                            $temp['code_display'] = FunctionLib::code_display($gifts[$r['gift_id']]['type'],$gifts[$r['gift_id']]['showbarcode'],2);
                            $temp['code_display_type'] = FunctionLib::code_display($gifts[$r['gift_id']]['type'],$gifts[$r['gift_id']]['showbarcode'],1);
                        }
                        #Trả thêm thông tin user & address cho APP là internal
                        if(FunctionLib::passInternal()){
                            $cartDetailInfo = DB::fetch("SELECT * FROM ".T_CART_DETAIL_INFO." WHERE cart_detail_id=".$r['id']." AND status=2 ORDER BY id DESC");
                            $temp['address_info'] = array();
                            if($cartDetailInfo && $r['address_id'] > 0){
                                $temp['address_info'] = array(
                                    "fullname" => $cartDetailInfo['fullname'],
                                    "email" => $cartDetailInfo['email'],
                                    "phone" => $cartDetailInfo['phone'],
                                    "indentify" => $cartDetailInfo['indentify'],
                                    "address" => $address
                                );
                            }
                        }
                        #End
                        #Remove thông tin với 1 số app bị chặn không lấy thông tin sử dụng Voucher
                        FunctionLib::appUsageCheck($agent_site,$temp);
                        #END
                        $detail[] = $temp;
                    }
                    $arr_return['detail'] = $detail;
                    $arr_return['item_quantity'] = count($detail);
                }
                if($shorten==1 && !empty($linkshorten)){
                    $shortenArr = Shorten::buildMuti($linkshorten);
                    $arr_return['linkCart'] = isset($shortenArr[$arr_return['linkCart']])?$shortenArr[$arr_return['linkCart']]:$arr_return['linkCart'];
                    $arr_return['linkCombo'] = isset($shortenArr[$arr_return['linkCombo']])?$shortenArr[$arr_return['linkCombo']]:$arr_return['linkCombo'];
                    if(isset($arr_return['detail']) && !empty($arr_return['detail'])){
                        foreach ($arr_return['detail'] as &$detailLink){
                            $detailLink['link'] = isset($shortenArr[$detailLink['link']])?$shortenArr[$detailLink['link']]:$detailLink['link'];
                        }
                    }
                }
                return System::apiSuccess($arr_return);
            } else {
                return System::apiError('Đơn hàng chưa thanh toán.',  ['status'=>102]);
            }
        }
        return System::apiError('Không tìm thấy dữ liệu.',  ['status'=>409]);
    }

    function getByTransactionVna()
    {die;
        $access_urbox = Url::getParam('app_secret', '');
        $agent_site = Url::getParamInt('app_id', 0);
        if (!Site::check_access_urbox($agent_site, $access_urbox)) {
            return System::apiError('Access Urbox không đúng.', false);
        }

        $cart_id = Url::getParamInt('cart_id', 0);
        $transaction_id = Url::getParam('transaction_id', 0);
        $cart = array();
        if ($cart_id > 0) {
            $cart = Cart::get($cart_id);

        } else {
            if ($transaction_id != '') {
                $cart = DB::fetch("SELECT * FROM " . T_CART . " WHERE transaction_id='" . $transaction_id . "'");
            }
        }

        if ($cart) {
            if ($cart['site_id'] != $agent_site) {
                return System::apiError('Không tìm thấy dữ liệu.', false);
            }
            $loyalOrder = DB::fetch("SELECT * FROM " . T_LOYAL_ORDER . " WHERE cart_id=" . $cart['id']);

            if ($loyalOrder['pay_status'] == 3) {

                $arr_return = array(
                    'id' => $cart['id'],
                    'created' => FunctionLib::dateFormat($cart['created'], 'd/m/Y H:i:s'),
                    'pay_time' => $cart['pay_time'] > 0 ? FunctionLib::dateFormat($cart['pay_time'], 'd/m/Y H:i:s') : '--',
                    'pay_status' => 'Đã thanh toán',
                    'money_total' => $loyalOrder['money'],
                    'miles_total' => $loyalOrder['point'],
                    'miles_total' => $loyalOrder['point'],
                    'site_user_id' => $cart['site_user_id'],
                    'receiver' => DB::fetch("SELECT fullname,phone,email,address FROM " . T_GIFT_RECEIVER . " WHERE id=" . $cart['receiver_id']),
                    'detail' => array(),
                );
                $re = DB::query("SELECT * FROM " . T_CART_DETAIL . " WHERE cart_id = " . $cart['id']);
                if ($re) {
                    $detail = array();
                    while ($r = @mysql_fetch_assoc($re)) {
                        $expired = '';
                        $code = '';
                        $link = LINK_API_URBOX . 'nhan-qua/' . $r['receive_code'] . '.html';
                        $gift = DB::fetch("SELECT * FROM " . T_GIFT_DETAIL . " WHERE id=" . $r['gift_detail_id']);
                        $brand = DB::fetch("SELECT * FROM " . T_BRAND . " WHERE id=" . $gift['brand_id']);
                        $cat = DB::fetch("SELECT * FROM " . T_CATEGORY . " WHERE id=" . $gift['cat_id']);
                        $delivery = DB::fetch("SELECT * FROM " . T_DELIVERY_DETAIL . " WHERE cart_detail_id=" . $r['id']);

                        $detail[] = array(
                            'link' => $link,
                            'delivery' => ($r['delivery'] == 3 ? 'Đã sử dụng' : 'Chưa sử dụng'),
                            'code' => $r['gift_detail_id'],
                            'gift_id' => $r['gift_id'],
                            'gift_name' => $gift['title'],
                            'brand_name' => $brand['title'],
                            'category_name' => $cat['title'],
                            'priceId' => $r['gift_detail_id'],
                            'money' => $r['money'],
                            'quantity' => $r['quantity'],
                            'expired' => $expired,
                            'plan_time' => (!empty($delivery)) ? FunctionLib::dateFormat($delivery['plan_time'], 'd/m/Y H:i:s') : '',
                            'finish_time' => (!empty($delivery)) ? FunctionLib::dateFormat($delivery['finish_time'], 'd/m/Y H:i:s') : ''
                        );
                    }
                    $arr_return['detail'] = $detail;
                }
                return System::apiSuccess($arr_return);
            } else {
                return System::apiError('Đơn hàng chưa thanh toán.', false);
            }
        }
        return System::apiError('Không tìm thấy dữ liệu.', false);
    }

    function getlist()
    {
        $access_urbox = Url::getParam('app_secret', '');
        $agent_site = Url::getParamInt('app_id', 0);
        if(!System::acceptApp($agent_site)){
            return System::apiError('Access Urbox không đúng.', ['status'=>211]);
        }
        if (!Site::check_access_urbox($agent_site, $access_urbox)) {
            return System::apiError('Access Urbox không đúng.', ['status'=>211]);
        }

        $cart_id = Url::getParam('cart_id', '');#Cach nhau boi dau ,
        $transaction_id = Url::getParam('transaction_id', '');#Cach nhau boi dau ,
        $campagin_code = Url::getParam('campagin_code', '');#Mã CP code
        $site_user_id = Url::getParam('site_user_id', '');#Cach nhau boi dau ,
        $startDate = Url::getParam('startDate', '');#Truyen Theo DInh dang d/m/y
        $endDate = Url::getParam('endDate', '');
        $per_page = Url::getParamInt('per_page', 50);
        $page_no = Url::getParamInt('page_no', 1);
        $reCache = Url::getParamInt('reCache', 1);
        $jsonLog = array(
            'cart_id' => $cart_id,
            'transaction_id' => $transaction_id,
            'site_user_id' => $site_user_id,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'per_page' => $per_page,
            'page_no' => $page_no,
        );
        #Ghi LOG các đối tác gọi đến 2-9-2022
        #LogsApi::insert('https://api.urbox.vn/2.0/cart/getlist',json_encode($jsonLog),'',$agent_site);
        if($transaction_id=='' && $site_user_id==''){
            $nameKey = 'Tool:App:GetList';
            $appKey = ReCache::get($nameKey);
            $appKeyArr = array();
            if($appKey){
                $appKeyArr = json_decode($appKey,true);
                $appKeyArr[$agent_site] = $agent_site;
            }else{
                $appKeyArr[$agent_site] = $agent_site;
            }
            ReCache::set($nameKey,json_encode($appKeyArr),0);
        }
        #End Ghi LOG
        $lang = Url::getParam('lang', 'vi');
        Language::$activeLang = $lang;
        Language::loadWordsFromLang(Language::$activeLang);
        $cond = array();
        $app_ids = Url::getParam('app_ids', '');#Cach nhau boi dau ,

        $timeEx = 15*60;

        if(FunctionLib::passInternal() && $app_ids!=''){
            $app_idsArr = explode(',',$app_ids);
            if(!empty($app_idsArr) && in_array($agent_site,$app_idsArr)){
                $cond[] = "site_id IN (" . implode(",",$app_idsArr) . ")";
                $timeEx = 15;#Đối với việc query APP muti sẽ cache 15s
            }else{
                $cond[] = 'site_id = ' . $agent_site;
            }
        }else{
            $cond[] = 'site_id = ' . $agent_site;
        }
        $cond[] = 'status = 2';
        if ($site_user_id != '') {
            $site_user_idArr = explode(',',$site_user_id);
            if(!empty($site_user_idArr)&&count($site_user_idArr)>1){
                $cond[] = "site_user_id IN ('" . implode("','",$site_user_idArr) . "')";
            }else{
                $cond[] = "site_user_id = '" . $site_user_id . "'";
            }
        }else{
            if($agent_site == 217 || $agent_site == 119){
                $cond[] = "site_user_id = 'CANPHAILAYDULIEUXXXX'";
            }
        }
        if ($cart_id != '') {
            $cart_Arr = explode(',',$cart_id);
            if(!empty($cart_Arr)){
                $cond[] = "id IN ('" . implode(',',$cart_Arr) . "')";
            }else{
                $cond[] = "id = ".((int) $cart_id);
            }
        }
        if ($transaction_id != '') {
            $transaction_Arr = explode(',',$transaction_id);
            if(!empty($transaction_Arr) && count($transaction_Arr)>1){
                $cond[] = "transaction_id IN ('" . implode("','",$transaction_Arr) . "')";
            }else{
                $cond[] = "transaction_id = '". $transaction_id ."'";
            }
        }
        if ($campagin_code != '') {
            $cond[] = "campaign_code = '". $campagin_code ."'";
        }
        $cond[] = 'pay_status = 2';
        $startDate_created = 0;
        if ($startDate != '') {
            $arrDate = explode('/', $startDate);
            if (!empty($arrDate)) {
                $startDate_created = mktime(0, 0, 0, (int)$arrDate[1], (int)$arrDate[0], (int)$arrDate[2]);
            }
        }

        $endDate_created = 0;
        if ($endDate != '') {
            $arrDate = explode('/', $endDate);
            if (!empty($arrDate)) {
                $endDate_created = mktime(23, 59, 59, (int)$arrDate[1], (int)$arrDate[0], (int)$arrDate[2]);
            }
        }
        if ($startDate_created > 0) {
            $condCard[] = 'created >= ' . $startDate_created;
            $cond[] = 'created >= ' . $startDate_created;
        }

        if ($endDate_created > 0) {
            $condCard[] = 'created <= ' . $endDate_created;
            $cond[] = 'created <= ' . $endDate_created;
        }

        $keyCache = 'CART:GETLIST:'.$agent_site.':'. md5($access_urbox.$agent_site.$cart_id.$transaction_id.$site_user_id.$app_ids.$startDate.$endDate.$per_page.$page_no.$lang);
        $giftListString = ReCache::get($keyCache);
        $dataReturn = json_decode($giftListString,true);
        #29-11-2023 Remove Cache khi call internal
        if($reCache == 2 && FunctionLib::passInternal()){
            $dataReturn = array();
            ReCache::set($keyCache, json_encode($dataReturn), $timeEx);
        }
        #2-2-2024 tạm thowiko cache 119
        /*if($agent_site == 119){
            $dataReturn = array();
            ReCache::set($keyCache, json_encode($dataReturn), $timeEx);
        }*/
        #END
        if (empty($dataReturn)) {
            $sql = "SELECT * FROM " . T_CART . " WHERE " . FunctionLib::addCondition($cond) . " ORDER BY id DESC";
            $re = Pagging::pager_query($sql, $per_page);
            $carts = array();
            if ($re) {

                while ($row = @mysql_fetch_assoc($re)) {
                    $key = md5($row['id'] . $row['customer_id'] . $row['receiver_id'] . $row['email'] . $row['phone'] . 'KIEMTRAMONQUACHUAN');
                    $linkCart = LINK_API_URBOX.'cart/'.$key.'.html?cart_no='.$row['id'];
                    $linkCombo = '';
                    if($row['combo_id'] > 0 ){
                        $linkCombo = DOMAIN_LOYAL . 'combo/'. $row['token'];
                    }
                    $carts[$row['id']] = array(
                        'id' => $row['id'],
                        'site_id' => $row['site_id'],
                        'customer_id' => $row['customer_id'],
                        'receiver_id' => $row['receiver_id'],
                        'createds' => $row['createds'],
                        'linkCart' => $linkCart,
                        'linkCombo' => $linkCombo,
                        'transaction_id' => $row['transaction_id'],
                        'campaign_code' => $row['campaign_code'],
                        'created' => FunctionLib::dateFormat($row['created'], 'd/m/Y'),
                        'created_timestamp' => $row['created'],
                        'pay_time' => $row['pay_time'] > 0 ? FunctionLib::dateFormat($row['pay_time'], 'd/m/Y') : '--',
                        'pay_status' => t('Đã thanh toán'),
                        'pay_status_code' => 2,
                        'detail' => array(),
                        'item_quantity' => 0,
                    );
                }
                if (!empty($carts)) {
                    $red = DB::query("SELECT * FROM " . T_CART_DETAIL . " WHERE cart_id IN(" . implode(',', array_keys($carts)) . ") AND status=2");
                    if ($red) {
                        $gifts = array();
                        $brands = array();
                        $giftCodes = array();
                        $gifts_id = array();
                        $gift_details = array();
                        $gift_details_id = array();
                        $cart_details_id = array();
                        $arrBrandId = array();
                        $arrTopup = array();
                        $arrDeliveryDetail = array();
                        $arrCartDetailInfo = array();

                        while ($rowd = @mysql_fetch_assoc($red)) {
                            $link = LINK_API_URBOX . 'nhan-qua/' . System::decrypt($rowd['receive_code']) . '.html';
                            if($rowd['type'] == 19 || $rowd['type'] == 22){
                                $link = DOMAIN_URCARD_LINK . System::decrypt($rowd['receive_code']);
                            }
                            $detail = array(
                                'id' => $rowd['id'],
                                'app_id' => $rowd['app_id'],
                                'urcard_id' => $rowd['urcard_id'],
                                'justGetOrder' => $rowd['justGetOrder'],
                                'link' => $link,
                                'type' => $rowd['type'],
                                'valuex' => $rowd['price'],#Ngày 6-5-2024 phát hiện ra đang trả lại price ko phải đơn hàng lên tạm thời chưa thay đổi dc do liên quan đến đối tác lên mình sẽ trả tạm
                                'usage_status' => t($rowd['delivery']==3?'Đã sử dụng':'Chưa sử dụng'),
                                'usage_status_code' => $rowd['delivery']==3?2:1,
                                'using_time' => $rowd['using_time']>0?FunctionLib::dateFormat($rowd['using_time'],'d/m/Y H:i'):'',
                                'gift_id' => $rowd['gift_id'],
                                'gift_detail_id' => $rowd['gift_detail_id'],
                                'delivery' =>t($rowd['delivery_required']==2?'Đang chờ xử lý':($rowd['delivery'] == 3 ? 'Đã sử dụng' : 'Chưa sử dụng')),
                                'deliveryCode' =>($rowd['delivery_required']==2?6:($rowd['delivery'] == 3 ? 2 : 1)),
                                'code_image' => '',
                                'delivery_required' => $rowd['delivery_required'],
                                'topup' => array()
                            );
                            if($rowd['delivery']>=4000 && $rowd['delivery']<=5000){
                                $detail['delivery'] = t('Hủy đơn hàng');
                                $detail['deliveryCode'] = 11;
                            }
                            $gifts_id[$rowd['gift_id']] = $rowd['gift_id'];
                            $cart_details_id[$rowd['id']] = $rowd['id'];
                            $gift_details_id[$rowd['gift_detail_id']] = $rowd['gift_detail_id'];
                            if (isset($carts[$rowd['cart_id']])) {
                                $carts[$rowd['cart_id']]['detail'][] = $detail;
                                $carts[$rowd['cart_id']]['item_quantity'] = $carts[$rowd['cart_id']]['item_quantity'] + 1;
                            }
                        }
                        if(!empty($cart_details_id)){
                            $reG = DB::query("SELECT * FROM " . T_GIFT_CODE . " WHERE cart_detail_id IN(" . implode(',', $cart_details_id) . ")");
                            if($reG){
                                while ($rowG = @mysql_fetch_assoc($reG)) {
                                    $giftCodes[$rowG['cart_detail_id']] = $rowG;
                                }
                            }
                            $reT = DB::query("SELECT id,member_id,firstname,lastname,process,note,cart_detail_id FROM ".T_ORDER_TOPUP." WHERE cart_detail_id IN (".implode(',',$cart_details_id).")");
                            if($reT){
                                while ($rowT = @mysql_fetch_assoc($reT)) {
                                    $arrTopup[$rowT['cart_detail_id']] = $rowT;
                                }
                            }
                            $reD = DB::query("SELECT id,cart_detail_id,process,agency_code FROM ".T_DELIVERY_DETAIL." WHERE cart_detail_id IN (".implode(',',$cart_details_id).")");
                            if($reD){
                                while ($rowD = @mysql_fetch_assoc($reD)) {
                                    $arrDeliveryDetail[$rowD['cart_detail_id']] = $rowD;
                                }
                            }
                            $reC = DB::query("SELECT id,cart_detail_id,metadata FROM ".T_CART_DETAIL_INFO." WHERE cart_detail_id IN (".implode(',',$cart_details_id).")");
                            if($reC){
                                while ($rowC = @mysql_fetch_assoc($reC)) {
                                    $arrCartDetailInfo[$rowC['cart_detail_id']] = $rowC;
                                }
                            }
                        }
                        if (!empty($gifts_id)) {
                            $gifts = DB::fetch_all("SELECT * FROM " . T_GIFT . " WHERE id IN(" . implode(',', $gifts_id) . ")");
                        }
                        $giftContents = array();
                        if (!empty($gift_details_id)) {
                            $reGD = DB::query("SELECT * FROM " . T_GIFT_DETAIL . " WHERE id IN(" . implode(',', $gift_details_id) . ")");
                            if($reGD){
                                while ($rowGD = @mysql_fetch_assoc($reGD)) {
                                    $arrBrandId[$rowGD['brand_id']] = $rowGD['brand_id'];
                                    $gift_details[$rowGD['id']] = $rowGD;
                                }
                                if(!empty($gift_details)){
                                    $rep = DB::query("SELECT gift_detail_id,price FROM " . T_GIFT_PRICE . " WHERE status=2 AND app_id=".$agent_site." AND  gift_detail_id IN(" . implode(',', array_keys($gift_details) ) . ")");
                                    if ($rep) {
                                        while ($rowp = @mysql_fetch_assoc($rep)) {
                                            if(isset($gift_details[$rowp['gift_detail_id']])){
                                                $gift_details[$rowp['gift_detail_id']]['price'] = $rowp['price'];
                                            }
                                        }
                                    }
                                }
                                if (!empty($gifts_id)) {
                                    $reg = DB::query("SELECT * FROM " . T_GIFT_CONTENT . " WHERE status=".IS_ON."  AND gift_id IN(" . implode(',', $gifts_id) . ") AND lang='" . $lang . "'");
                                    if ($reg) {
                                        while ($rowg = @mysql_fetch_assoc($reg)) {
                                            $giftContents[$rowg['gift_id']] = $rowg;
                                        }
                                    }
                                }
                            }
                        }
                        if (!empty($arrBrandId)) {
                            $brands = DB::fetch_all("SELECT id,logo,title FROM " . T_BRAND . " WHERE id IN(" . implode(',', $arrBrandId) . ")");
                        }
                        if (!empty($carts)) {
                            foreach ($carts as &$cart) {
                                if (!empty($cart['detail'])) {
                                    foreach ($cart['detail'] as &$detail) {
                                        if($detail['justGetOrder'] == IS_YES){
                                            $detail['link'] = DOMAIN_LOYAL . 'grab.reward/gift/code/' . $detail['gift_detail_id'];
                                        }
                                        if (isset($gifts[$detail['gift_id']])) {
                                            $detail['gift_title'] = $gifts[$detail['gift_id']]['name'];
                                            $detail['usage_check'] = $gifts[$detail['gift_id']]['code_type']==2?0:1;
                                            if (isset($gifts[$detail['gift_id']]['type'])) {
                                                if($gifts[$detail['gift_id']]['type'] == 4){
                                                    $detail['link'] = DOMAIN_LOYAL . 'card/' . System::decrypt($giftCodes[$detail['id']]['code']);
                                                }else if($gifts[$detail['gift_id']]['type'] == 14){
                                                    if(isset(System::$data['point_unit'][$gift['pointu_id']])){
                                                        $detail['code'] = '';
                                                        $detail['link'] = System::$data['point_unit'][$gifts[$detail['gift_id']]['pointu_id']]['url'] . $cart['site_user_id'];
                                                    }
                                                }
                                            }
                                        }
                                        if (isset($giftCodes[$detail['id']])) {
                                            $detail['expired'] = $giftCodes[$detail['id']]['expired']>0?FunctionLib::dateFormat($giftCodes[$detail['id']]['expired'],'d/m/Y H:i'):t('Vô hạn');
                                            $detail['code'] = StringLib::vibReturnCode(System::decrypt($giftCodes[$detail['id']]['code']),$lang);
                                            $detail['serial'] = $giftCodes[$detail['id']]['serial'];
                                            if($giftCodes[$detail['id']]['expired'] > 0 && $giftCodes[$detail['id']]['expired']< TIME_NOW &&  $giftCodes[$detail['id']]['active']==1){
                                                $detail['delivery'] = t('Hết hạn');
                                                $detail['deliveryCode'] = 4;
                                                $detail['usage_status'] = t('Hết hạn');
                                                $detail['usage_status_code'] = 3;
                                            }
                                            if (isset($gifts[$detail['gift_id']])) {
                                                $detail['code_image']  = Cart::genImageCode($gifts[$detail['gift_id']]['showbarcode'],$gifts[$detail['gift_id']]['brand_id'],System::decrypt($giftCodes[$detail['id']]['code']));
                                                /*if ($gifts[$detail['gift_id']]['showbarcode'] == 2) {
                                                    if($gifts[$detail['gift_id']]['brand_id']==305){
                                                        $detail['code_image'] = 'https://barcode.tec-it.com/barcode.ashx?data=' . urlencode(System::decrypt($giftCodes[$detail['id']]['code'])) . '&code=EAN13&multiplebarcodes=false&translate-esc=false&unit=Fit&dpi=96&imagetype=Gif&rotation=0&color=%23000000&bgcolor=%23ffffff&qunit=Mm&quiet=0';
                                                    }else{
                                                        $detail['code_image'] = 'https://barcode.tec-it.com/barcode.ashx?data=' . urlencode(System::decrypt($giftCodes[$detail['id']]['code'])) . '&code=Code128&multiplebarcodes=false&translate-esc=false&unit=Fit&dpi=96&imagetype=Gif&rotation=0&color=%23000000&bgcolor=%23ffffff&qunit=Mm&quiet=0&dmsize=Default';
                                                    }
                                                } else {
                                                    $detail['code_image'] = 'https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=' . urlencode(System::decrypt($giftCodes[$detail['id']]['code']));
                                                }*/

                                                $detail['code_display'] = FunctionLib::code_display($gifts[$detail['gift_id']]['type'],$gifts[$detail['gift_id']]['showbarcode'],2);
                                                $detail['code_display_type'] = FunctionLib::code_display($gifts[$detail['gift_id']]['type'],$gifts[$detail['gift_id']]['showbarcode'],1);
                                            }
                                        }
                                        #Qua Dragopass type 23 thì lấy thông tin chi tiết thêm
                                        if ($detail['type']==23 && isset($arrCartDetailInfo[$detail['id']])) {
                                            $metadata = json_decode($arrCartDetailInfo[$detail['id']]['metadata'],true);
                                            if(!empty($metadata)&& isset($metadata['codeFastTrackDragonpass'])){
                                                $detail['codeFastTrackDragonpass'] = $metadata['codeFastTrackDragonpass'];
                                            }
                                        }
                                        if (isset($arrTopup[$detail['id']])) {
                                            $detail['topup'] = [
                                                'member_id' => $arrTopup[$detail['id']]['member_id'],
                                                'firstname' => $arrTopup[$detail['id']]['firstname'],
                                                'lastname' => $arrTopup[$detail['id']]['lastname'],
                                                'process' => $arrTopup[$detail['id']]['process'],
                                                'usage_process' => $arrTopup[$detail['id']]['process']==1?t('Chờ duyệt'):(($arrTopup[$detail['id']]['process']==2)?t('Chờ nạp'):($arrTopup[$detail['id']]['process']==3?t('Nạp dặm thành công'):t('Nạp dặm thất bại'))),
                                                'note' => ($arrTopup[$detail['id']]['process']==2)?'Waiting for topup':$arrTopup[$detail['id']]['note'],
                                            ];
                                        }

                                        if (isset($arrDeliveryDetail[$detail['id']]) && $detail['delivery'] == 'Đang chờ xử lý') {
                                            $detail['delivery'] = t(empty($arrDeliveryDetail[$detail['id']])?'Đang chờ xử lý':($arrDeliveryDetail[$detail['id']]['process']==1?'Đang chờ xử lý':($arrDeliveryDetail[$detail['id']]['process']==2?'Đã chuyển hàng':($arrDeliveryDetail[$detail['id']]['process']==3?'Thành công':($arrDeliveryDetail[$detail['id']]['process']==4?'Vận chuyển trả hàng':'Chờ có hàng')))));
                                            $detail['deliveryCode'] = empty($arrDeliveryDetail[$detail['id']])?2:($arrDeliveryDetail[$detail['id']]['process']==1?6:($arrDeliveryDetail[$detail['id']]['process']==2?7:($arrDeliveryDetail[$detail['id']]['process']==3?8:($arrDeliveryDetail[$detail['id']]['process']==4?9:10))));
                                            $detail['estimateDelivery'] = ($detail['delivery_required'] == 2 ? t('Dự kiến giao hàng từ 3 - 5 ngày làm việc.'):'');
                                            $detail['delivery_tracking'] = ($detail['delivery_required'] == 2 ? $arrDeliveryDetail[$detail['id']]['agency_code']:'');
                                        }
                                        if (isset($gift_details[$detail['gift_detail_id']])) {
                                            $images = MediaUrl::fromImageTitle($gift_details[$detail['gift_detail_id']]['avatar']);
                                            $image_rectanges = MediaUrl::fromImageTitle($gift_details[$detail['gift_detail_id']]['image_rectange']);
                                            $detail['gift_detail_title'] = $gift_details[$detail['gift_detail_id']]['title'];
                                            if($lang == 'en'){
                                                $detail['gift_detail_title'] =  StringLib::post_db_parse_html($giftContents[$detail['gift_id']]['title']) . ' ' .  FunctionLib::numberFormatcomment($gift_details[$detail['gift_detail_id']]['price']).' VND';
                                                $detail['gift_title'] =  StringLib::post_db_parse_html($giftContents[$detail['gift_id']]['title']);
                                            }
                                            $detail['price'] = $gift_details[$detail['gift_detail_id']]['price'];
                                            $detail['image'] = isset($images[640]) ? $images[640] : '';
                                            $detail['images_rectangle'] = isset($image_rectanges[640]) ? $image_rectanges[640] : '';
                                            if (isset($brands[$gift_details[$detail['gift_detail_id']]['brand_id']])) {
                                                $detail['brandId'] = $brands[$gift_details[$detail['gift_detail_id']]['brand_id']]['id'];
                                                $detail['brandTitle'] = $brands[$gift_details[$detail['gift_detail_id']]['brand_id']]['title'];
                                                $imageBrand = MediaUrl::fromImageTitle($brands[$gift_details[$detail['gift_detail_id']]['brand_id']]['logo']);
                                                $detail['brandImage'] = isset($imageBrand[640]) ? $imageBrand[640] : '';
                                            }
                                        }

                                        #Remove thông tin với 1 số app bị chặn không lấy thông tin sử dụng Voucher
                                        FunctionLib::appUsageCheck($agent_site,$detail);
                                        #END
                                    }
                                }
                            }
                        }
                    }
                }
            }
            if($carts){
                $dataReturn = array('cart'=>array_values($carts),'push_data'=>array('key'=>'totalPage','data'=>Pagging::$totalPage));
                ReCache::set($keyCache, json_encode($dataReturn), $timeEx);
                return System::apiSuccess($dataReturn['cart'],$dataReturn['push_data']);
            }else{
                return System::apiError('Không tìm thấy dữ liệu', ['status'=>409]);
            }
        }else{
            return System::apiSuccess($dataReturn['cart'],$dataReturn['push_data']);
        }

    }

	function getBalance()
	{die;
		$access_urbox = Url::getParam('app_secret', '');
		$agent_site = Url::getParamInt('app_id', 0);
		if (!Site::check_access_urbox($agent_site, $access_urbox)) {
			return System::apiError('Access Urbox không đúng.', ['status'=>211]);
		}

		$site_user_id = Url::getParam('site_user_id', '');
		$per_page = Url::getParamInt('per_page', 50);
		$page_no = Url::getParamInt('page_no', 1);
		if($per_page > 100) $per_page = 100;
		$cond = array();

		$cond[] = 'status = 2';
		$cond[] = 'pay_status = 2';
		$cond[] = 'type = 4';
		$cond[] = 'app_id = ' . $agent_site;

		if ($site_user_id != '') {
			$site_user_idArr = explode(',',$site_user_id);
			if(!empty($site_user_idArr)){
				$condc = "site_user_id IN ('" . implode("','",$site_user_idArr) . "')";
			}else{
				$condc = "site_user_id = '" . $site_user_id . "'";
			}
			$carts = DB::fetch_all("SELECT id,transaction_id from ". T_CART . " WHERE $condc AND status = 2");
			if(!empty($carts)) {
				$carts_ids = array_column($carts,'id');
				$carts_id = implode( ',', $carts_ids );
				$cond[] = "cart_id in ($carts_id)";
			}else{
				return System::apiError('Không tìm thấy dữ liệu', ['status'=>409]);
			}
		}
		$conds = FunctionLib::addCondition($cond);
		$sql_cart = "SELECT id from " . T_CART_DETAIL . " WHERE $conds ";
		$re = Pagging::pager_query($sql_cart, $per_page,$page_no);
		if ($re) {
			$arr_cart = [];
			while ($row = @mysql_fetch_assoc($re)) {
				$arr_cart[] = $row['id'];
			}
			$data = [];
			if (!empty($arr_cart)) {
				$red = DB::query("SELECT id,cart_detail_id,cart_id,code FROM " . T_GIFT_CODE . " WHERE cart_detail_id IN(" . implode(',', $arr_cart) . ") AND status=1");
				if ($red) {
					$array_token = [];
					$array_token_cart = [];
					while ($rowd = @mysql_fetch_assoc($red)) {
						$array_token[$rowd['cart_id']] = $rowd['code'];
						$array_token_cart[$rowd['code']] = $rowd['cart_id'];
					}
					if (!empty($array_token)) {
						$str_token = implode("','",$array_token);
						$str_cart = implode(",",$array_token_cart);
						$carts_token = DB::fetch_all("SELECT id,site_user_id,transaction_id from ". T_CART . " WHERE id in($str_cart) AND status = 2");
						$card = DB::fetch_all("SELECT * FROM " . T_CARD . " WHERE token in('$str_token') and app_id={$agent_site}");
						foreach ($card as $item) {
							$status =  "";
							switch ($item['status']){
								case 1: $status = "Chưa kích hoạt";
									break;
								case 2: $status = "Đã kích hoạt";
									break;
								case 3: $status = "Khóa";
									break;
								case 4: $status = "Ban";
									break;
								case 5: $status = "Chuyển vào app";
									break;
							}
							$data[] = [
								'money' => $item['money'],
								'phone' => $item['phone'],
								'status' => $status,
								'create_time' => date('d/m/Y H:i:s',$item['created']),
								'active_time' => $item['active_time'] > 0? date('d/m/Y H:i:s',$item['active_time']): 0,
								'lock_time' => $item['lock_time'] > 0?date('d/m/Y H:i:s',$item['lock_time']):0,
								'ban_time' => $item['ban_time'] > 0?date('d/m/Y H:i:s',$item['ban_time']):0,
								'expired_time' => $item['expired_time'] > 0?date('d/m/Y H:i:s',$item['expired_time']):0,
								'link' => Shorten::build("https://page.urbox.vn/card/". System::decrypt($item['token'])),
								'transaction_id' => $carts_token[$array_token_cart[$item['token']]]['transaction_id'],
								'site_user_id' => $carts_token[$array_token_cart[$item['token']]]['site_user_id'],
							];
						}
						return System::apiSuccess($data,array('key'=>'totalPage','data'=>Pagging::$totalResult));
					}
				}
			}
		}
		return System::apiError('Không tìm thấy dữ liệu', ['status'=>409]);
	}

    /**
     * @return array
     * **@version 2.0
     * @function resendSms gửi lại tin nhắn cho người nhận quà
     * @var int|null $phone số điện thoại cần gửi tin nhắn
     * @var int $cart_detail_id mã chi tiết đơn hàng
     * <AUTHOR>
     */
    function resendSms()
    {die;
        $cart_detail_id = Url::getParamInt('cart_detail_id', '');
        $phone = Url::getParam('phone', '');
        if (empty($cart_detail_id) || !is_int($cart_detail_id)) {
            return System::apiError("Quà này không tồn tại!");
        }
        $sql = "SELECT id FROM " . T_CART_DETAIL . " where id={$cart_detail_id} AND sendSms=1 AND status=" . IS_YES . " LIMIT 1";
        $cart_detail = DB::query($sql);
        $row = mysql_num_rows($cart_detail);
        //echo $sql_sms = "INSERT INTO " . T_SMS_SEND . " (cat_id,cart_detail_id, phone, content) SELECT cat_id,cart_detail_id, phone, content FROM " . T_SMS_SEND . " WHERE cart_detail_id={$cart_detail_id} limit 1";

        if ($row > 0) {
            $sql_sms = "INSERT INTO " . T_SMS_SEND . " (cat_id,cart_detail_id, phone, content) SELECT cat_id,cart_detail_id, phone, content FROM " . T_SMS_SEND . " WHERE cart_detail_id={$cart_detail_id} limit 1";
            DB::query($sql_sms);
            return System::apiSuccess("Đã gửi lại SMS");
        } else {
            return System::apiError("Không có quà này!");
        }

    }

    /**
     * @return array
     * **@version 2.0
     * @function giftLink lấy link quà
     * @var int $cart_detail_id mã chi tiết đơn hàng
     * <AUTHOR>
     */

    function giftLink()
    {die;
        $cart_detail_id = Url::getParamInt('cart_detail_id', '');
        if (empty($cart_detail_id) || !is_int($cart_detail_id)) {
            die("Mã đơn hàng không tồn tại!");
        }
        $sql = "SELECT receive_code FROM " . T_CART_DETAIL . " where id={$cart_detail_id} LIMIT 1";
        $cart = DB::fetch($sql);
        if ($cart['receive_code'] != null) {
            $link = "https://urbox.vn/nhan-qua/" . System::decrypt($cart['receive_code']) . '.html';
            return System::apiSuccess(array('link' => $link));
        } else {
            return System::apiError("Quà này không tồn tại!");
        }

    }

    /**
     * @return array
     * **@var int $receiver_code mã chi tiết đơn hàng
     * @version 2.0
     * @function updateCart cập nhật lại giá tiền và xoá cart_detail theo receiver code
     */

    function updateCart()
    {die;
        $receive_code = Url::getParam('receive_code', '');
        if (empty($receive_code)) {
            return System::apiError("Quà này không tồn tại!");
        }
        DB::query("UPDATE cart_detail set status = -1 where receive_code='{$receive_code}'");
        $cart = DB::fetch("SELECT cart_id,money FROM " . T_CART_DETAIL . " WHERE receive_code='$receive_code'");

        if (!empty($cart)) {
            $list_cart = DB::fetch("select count(id) as soluong from " . T_CART_DETAIL . " where cart_id={$cart['cart_id']}");
            if ($list_cart['soluong'] <= 1) {
                DB::query("UPDATE " . T_CART . " set status = -1 where id= {$cart['cart_id']}");
            } else {
                DB::query("UPDATE " . T_CART . "  set money_gift = (money_gift - {$cart['money']}) ,money_total = (money_total - {$cart['money']}) where id= {$cart['cart_id']}");
            }
            return System::apiError("Đã cập nhật đơn hàng !");
        }
        return System::apiError("Đơn hàng không có quà !");
    }

    /**
     * @function infoCode Lấy thông tin chi tiết 1 code kèm theo các thong tin cơ bản của quả theo receiver code
     */
    function infoCode()
    {
die;
        #Kiem tra access_urbox
        $access_urbox = Url::getParam('access_urbox', '');
        $agent_site = Url::getParam('agent_site', '');

        $app_secret = Url::getParam('app_secret', '');
        $app_id = Url::getParam('app_id', '');

        if ($app_secret != '') {
            $access_urbox = $app_secret;
        }
        if ($app_id != '') {
            $agent_site = $app_id;
        }

        if (!Site::check_access_urbox($agent_site, $access_urbox)) {
            return System::apiError('Access Urbox không đúng.', false);
        }

        $receive_code = Url::getParam('token', '');
        $lang = Url::getParam('lang', 'vi');
        if ($receive_code == '') {
            return System::apiError('Không tìm thấy TOKEN.', false);
        }

        $cart_detail = DB::fetch("SELECT * FROM  " . T_CART_DETAIL . " WHERE status=" . IS_ON . " AND receive_code='" . System::decrypt($receive_code) . "'");
        if (empty($cart_detail)) {
            return System::apiError('Không tìm thấy thông tin TOKEN.', false);
        }
        $data = array();
        #Thong tin Bộ quà
        $gift = DB::fetch("SELECT * FROM " . T_GIFT . " WHERE id=" . $cart_detail['gift_id']);
        #Thong Tin CODE
        $giftCode = DB::fetch("SELECT * FROM " . T_GIFT_CODE . " WHERE status=1 AND cart_id=" . $cart_detail['cart_id'] . " AND cart_detail_id=" . $cart_detail['id']);
        $data['code'] = array();
        if (!empty($giftCode)) {
            $giftCode['code'] = System::decrypt($giftCode['code']);
            $linkBarCode = '';
            if ($gift['showbarcode'] == 2) {
                if ($gift['brand_id'] == 305) {
                    $linkBarCode = 'https://barcode.tec-it.com/barcode.ashx?data=' . urlencode($giftCode['code']) . '&code=EAN13&multiplebarcodes=false&translate-esc=false&unit=Fit&dpi=96&imagetype=Gif&rotation=0&color=%23000000&bgcolor=%23ffffff&qunit=Mm&quiet=0';
                } else {
                    $linkBarCode = 'https://barcode.tec-it.com/barcode.ashx?data=' . urlencode($giftCode['code']) . '&code=Code128&multiplebarcodes=false&translate-esc=false&unit=Fit&dpi=96&imagetype=Gif&rotation=0&color=%23000000&bgcolor=%23ffffff&qunit=Mm&quiet=0&dmsize=Default';
                }

            } else {
                #$curl = New CURL();
                #$returned_data = $curl->get('https://api.urbox.vn/1.0/tool/qr?data='.urlencode($arrCartDetail['qrCodeUrl']));
                #$data = json_decode($returned_data, true);
                #$arrCartDetail['giftCode']['qrCode'] = isset($data['data']['src'])?$data['data']['src']:'https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=' . urlencode($arrCartDetail['qrCodeUrl']);
                $linkBarCode = 'https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=' . urlencode($giftCode['code']);
            }

            $data['code'] = array(
                'code' => $giftCode['code'],
                'expired' => $giftCode['expired'] == 0 ? 'Vô hạn' : FunctionLib::dateFormat($giftCode['expired'], 'd/m/y h:i'),
                'cta' => "https://urbox.vn/nha-cung-cap/kich-hoat-otp/" . md5('NCCKICHHOATQUA' . $giftCode['code']) . ".html?codegift={$giftCode['code']}",
                'qr' => $linkBarCode
            );
        }
        #Thong tin Qua
        $giftDetail = DB::fetch("SELECT * FROM " . T_GIFT_DETAIL . " WHERE id=" . $cart_detail['gift_detail_id']);
        $data['gift'] = array();
        if (!empty($giftDetail)) {
            $images = MediaUrl::fromImageTitle($giftDetail['avatar']);
            $giftContent = DB::fetch("SELECT * FROM " . T_GIFT_CONTENT . " WHERE lang='" . $lang . "' and gift_id=" . $giftDetail['gift_id']);
            $data['gift'] = array(
                'title' => $giftDetail['title'],
                'condition' => isset($giftContent['note']) ? $giftContent['note'] : '',
                'images' => isset($images[640]) ? $images[640] : '',
                'brand' => array()
            );
            $brand = DB::fetch("SELECT * FROM " . T_BRAND . " WHERE id=" . $giftDetail['brand_id']);
            if (!empty($brand)) {
                $imageBrand = MediaUrl::fromImageTitle($brand['logo']);
                $office = Brand::getOffice(array('brand_id' => (int)$brand['id'], 'status' => IS_ON), ['id', 'address', 'latitude', 'longitude', 'brand_id']);
                $data['gift']['brand'] = array(
                    'title' => $brand['title'],
                    'logo' => isset($imageBrand[640]) ? $imageBrand[640] : '',
                    'office' => !empty($office) ? array_values($office) : array()
                );
            }
        }
        return System::apiSuccess($data);

    }

    function createdVpbank()
    {
        die;
        $re = DB::query("SELECT * FROM _gift_by_user WHERE type=1");
        $msg = '';
        if ($re) {
            $data = array();
            while ($row = @mysql_fetch_assoc($re)) {
                $data[$row['code']][] = $row;
            }
            if (!empty($data)) {
                foreach ($data as $key => $dt) {

                    $dataBuy = array();
                    $email = '';
                    $phone = '';
                    $name = '';

                    if (!empty($dt)) {
                        foreach ($dt as $item) {
                            $dataBuy[] = array('priceId' => $item['supplier_code'], 'quantity' => $item['quantity']);
                            $email = $item['email'];
                            $phone = $item['phone'];
                            $name = $item['name'];
                        }
                    }

                    $curl = new CURL();
                    $post = array(
                        'ttmessage' => "Chúc mừng bạn nhận một Vourcher",
                        'ttemail' => $email,
                        'ttphone' => $phone,
                        'ttfullname' => $name,
                        'callback_transaction' => 1,
                        'isSendSms' => 1,
                        'transaction_id' => $key,
                        'app_id' => 36,
                        'app_secret' => 'eb8424c3b4154accb3317503e693ccdf',

                        'dataBuy' => json_encode($dataBuy)
                    );

                    $ex = $curl->post('http://api.urbox.internal/2.0/cart/cartPayVoucher',$post);
                    $response = json_decode($ex,true);

                    if ($response['done'] == 1) {
                        DB::update('_gift_by_user', array('type' => 2), "code='" . $key . "'");
                    }
                    $msg .= $response['msg'] . '<br/>';

                }
            }
        }
        return System::apiSuccess($msg);
    }

    function runLazada()
    {
        exit;
        $re = DB::query("SELECT * FROM push_lazada WHERE status=1");
        $msg = '';
        if ($re) {
            $data = array();
            while ($row = @mysql_fetch_assoc($re)) {
                $data[$row['madonsendo']][] = $row;
            }

            if (!empty($data)) {
                foreach ($data as $key => $dt) {

                    $dataBuy = array();
                    $email = '';
                    $phone = '';
                    $name = '';

                    if (!empty($dt)) {
                        foreach ($dt as $item) {
                            $dataBuy[] = array('priceId' => $item['idqua'], 'quantity' => $item['soluong']);
                            $_phone = $item['sdt'];
                            if ($_phone[0] != 0) {
                                $phone = '0' . $item['sdt'];
                            } else {
                                $phone = $item['sdt'];
                            }
                        }
                    }

                    $curl = new CURL();
                    $post = array(
                        'ttmessage' => "Chúc mừng bạn nhận một Voucher",
                        'ttemail' => $email,
                        'ttphone' => $phone,
                        'ttfullname' => $name,
                        'isSendSms' => 1,
                        'callback_transaction' => 1,
                        'shorten' => 1,
                        'transaction_id' => $key,
                        'app_id' => 114,
                        'app_secret' => 'ba0e02d1da43d29e1f4f2224678ee5bb',
                        'dataBuy' => json_encode($dataBuy)
                    );

                    $ex = $curl->post('http://api.urbox.internal/2.0/cart/cartPayVoucher',$post);
                    $response = json_decode($ex,true);

                    if ($response['done'] == 1) {
                        DB::update('push_lazada', array('status' => 2), "madonsendo='" . $key . "'");
                    }
                    $msg .= $response['msg'] . '<br/>';

                }
            }
        }
        return System::apiSuccess($msg);
    }

    function runSendo()
    {
        exit;
        $re = DB::query("SELECT * FROM push WHERE status=1");
        $msg = '';
        if ($re) {
            $data = array();
            while ($row = @mysql_fetch_assoc($re)) {
                $data[$row['madonsendo']][] = $row;
            }

            if (!empty($data)) {
                foreach ($data as $key => $dt) {

                    $dataBuy = array();
                    $email = '';
                    $phone = '';
                    $name = '';

                    if (!empty($dt)) {
                        foreach ($dt as $item) {
                            $dataBuy[] = array('priceId' => $item['idqua'], 'quantity' => $item['soluong']);
                            $_phone = $item['sdt'];
                            if ($_phone[0] != 0) {
                                $phone = '0' . $item['sdt'];
                            } else {
                                $phone = $item['sdt'];
                            }
                        }
                    }

                    $curl = new CURL();
                    $post = array(
                        'ttmessage' => "Chúc mừng bạn nhận một Voucher",
                        'ttemail' => $email,
                        'ttphone' => $phone,
                        'ttfullname' => $name,
                        'isSendSms' => 1,
                        'callback_transaction' => 1,
                        'shorten' => 1,
                        'transaction_id' => $key,
                        'app_id' => 63,
                        'app_secret' => '3ead1150636eed18b2f7e3b19e864325',
                        'dataBuy' => json_encode($dataBuy)
                    );

                    $ex = $curl->post('http://api.urbox.internal/2.0/cart/cartPayVoucher',$post);
                    $response = json_decode($ex,true);

                    if ($response['done'] == 1) {
                        DB::update('push', array('status' => 2), "madonsendo='" . $key . "'");
                    }
                    $msg .= $response['msg'] . '<br/>';

                }
            }
        }
        return System::apiSuccess($msg);
    }

    function runTopupDel()
    {die;
        $id = Url::getParamInt('id', 0);
        $isSendSms = Url::getParamInt('isSendSms', 0);
        $pass = Url::getParam('pass', '');

        if ($pass != FunctionLib::dateFormat(TIME_NOW, 'dmYH')) {
            return System::apiError("Có lỗi xảy ra");
        }
        if ($id <= 0) {
            return System::apiError("Không tìm thấy đơn hàng");
        }
        $cart = DB::fetch("select * FROM cart where id=" . $id . "  LIMIT 1");
        if (empty($cart)) {
            return System::apiError("Không tìm thấy đơn hàng");
        }
        if ($cart['status'] == 2 && $cart['pay_status'] == 2) {
            return System::apiError("ĐƠn hàng bình thường nhé");
        }
        if ($cart['type'] == 10) {
            return System::apiError("ĐƠn hàng đã chạy lại 1 lần");
        }
        $re = DB::query("SELECT * FROM " . T_CART_DETAIL . " WHERE cart_id=" . $cart['id']);
        $dataBuy = array();
        if ($re) {
            while ($rowA = @mysql_fetch_assoc($re)) {
                if (!empty($dataBuy)) {
                    foreach ($dataBuy as $key => &$dataB) {
                        if ($dataB['priceId'] == $rowA['gift_detail_id']) {
                            $dataB['quantity'] = $dataB['quantity'] + 1;
                        } else {
                            $dataBuy[] = array('priceId' => $rowA['gift_detail_id'], 'quantity' => 1, 'amount' => $rowA['money']);
                        }
                    }
                } else {
                    $dataBuy[] = array('priceId' => $rowA['gift_detail_id'], 'quantity' => 1, 'amount' => $rowA['money']);
                }
            }
            $app = DB::fetch("SELECT * FROM " . T_APP . " WHERE id=" . $cart['site_id']);
            if ($app['card_id'] == $cart['card_id']) {
                $curl = new CURL();
                $post = array(
                    'ttmessage' => $cart['message'],
                    'ttemail' => '',
                    'ttphone' => DB::fetch('SELECT phone FROM ' . T_GIFT_RECEIVER . " WHERE id=" . $cart['receiver_id'], 'phone'),
                    'ttfullname' => '',
                    'isSendSms' => $isSendSms,
                    'callback_transaction' => 1,
                    'transaction_id' => $cart['transaction_id'],
                    'site_user_id' => $cart['site_user_id'],
                    'app_id' => $app['id'],
                    'app_secret' => $app['access_key'],
                    'dataBuy' => json_encode($dataBuy)
                );

                $ex = $curl->post('http://api.urbox.internal/2.0/cart/cartPayVoucher',$post);
                $response = json_decode($ex,true);
            }else{
                $curl = new CURL();
                $post = array(
                    'ttmessage' => $cart['message'],
                    'ttemail' => '',
                    'ttphone' => DB::fetch('SELECT phone FROM ' . T_GIFT_RECEIVER . " WHERE id=" . $cart['receiver_id'], 'phone'),
                    'ttfullname' => '',
                    'isSendSms' => $isSendSms,
                    'callback_transaction' => 1,
                    'transaction_id' => $cart['transaction_id'] != '' ? $cart['transaction_id'] : $id . 'CHAYLAI',
                    'site_user_id' => $cart['site_user_id'],
                    'agent_site' => $app['id'],
                    'card_id' => $cart['card_id'],
                    'access_urbox' => $app['access_key'],
                    'dataBuy' => json_encode($dataBuy)
                );

                $ex = $curl->post('https://api.urbox.vn/2.0/cart/cartApp', $post);
                $response = json_decode($ex, true);
            }


            if ($response['done'] == 1) {
                DB::update(T_CART, array('type' => 10), "id='" . $cart['id'] . "'");
                return System::apiSuccess("Thành công");
            } else {
                return System::apiError("Có lỗi xảy ra", array('response' => $response));
            }
        }
        return System::apiError("Có lỗi xảy ra");
    }

    function acacy()
    {
        die;
        $re = DB::query("select * FROM cart where id=158842 and type=1 LIMIT 5");
        $msg = '';
        if ($re) {
            $data = array();
            while ($row = @mysql_fetch_assoc($re)) {
                $dataBuy = array();
                $cart_detail = DB::query("SELECT * FROM " . T_CART_DETAIL . " WHERE cart_id=" . $row['id']);
                if ($cart_detail) {
                    while ($rowA = @mysql_fetch_assoc($cart_detail)) {
                        if (!empty($dataBuy)) {
                            foreach ($dataBuy as $key => &$dataB) {
                                if ($dataB['priceId'] == $rowA['gift_detail_id']) {
                                    $dataB['quantity'] = $dataB['quantity'] + 1;
                                } else {
                                    $dataBuy[] = array('priceId' => $rowA['gift_detail_id'], 'quantity' => 1, 'amount' => $rowA['money']);
                                }
                            }
                        } else {


                        }
                    }
                }
                $curl = new CURL();
                $post = array(
                    'ttmessage' => $row['message'],
                    'ttemail' => '',
                    'ttphone' => DB::fetch('SELECT phone FROM ' . T_GIFT_RECEIVER . " WHERE id=" . $row['receiver_id'], 'phone'),
                    'ttfullname' => '',
                    'isSendSms' => 1,
                    'callback_transaction' => 1,
                    'transaction_id' => $row['transaction_id'],
                    'site_user_id' => $row['site_user_id'],
                    'app_id' => 88,
                    'app_secret' => '7e6bbc71907053f9df792e2f7297abc8',
                    'dataBuy' => json_encode($dataBuy)
                );

                $ex = $curl->post('http://api.urbox.internal/2.0/cart/cartPayVoucher',$post);
                $response = json_decode($ex,true);

                if ($response['done'] == 1) {
                    DB::update(T_CART, array('type' => 10), "id='" . $row['id'] . "'");
                }
                $msg .= $response['msg'] . '<br/>';
            }

        }
        return System::apiSuccess($msg);
        /*
                $re = DB::query("select * FROM accy where status=1 LIMIT 5");
                $msg = '';
                if($re){
                    $data = array();
                    while ($row = @mysql_fetch_assoc($re)){
                        $dataPosst = json_decode($row['data'],true);

                        $curl = new CURL();
                        $post = array(
                            'ttmessage' => $dataPosst['ttmessage'],
                            'ttemail' => '',
                            'ttphone' => $dataPosst['ttphone'],
                            'ttfullname' => '',
                            'isSendSms' => 1,
                            'callback_transaction' => 1,
                            'transaction_id' => $dataPosst['transaction_id'].'BACK',
                            'site_user_id' => $dataPosst['site_user_id'],
                            'app_id' => 88,
                            'app_secret' => '7e6bbc71907053f9df792e2f7297abc8',
                            'dataBuy' => json_encode($dataPosst['dataBuy'])
                        );

                        $ex = $curl->post('https://api.urbox.vn/2.0/cart/cartPayVoucher',$post);
                        $response = json_decode($ex,true);

                        if($response['done']==1){
                            DB::update('accy',array('status'=>2),"id='".$row['id']."'");
                        }
                        $msg .=  $response['msg'].'<br/>';
                    }

                }
                return System::apiSuccess($msg);*/
    }

    function createdCombo()
    {

        /*$dataBuy[] = array('id'=>1162,'priceId'=> 2735,'quantity'=>1);
        $post = array(
            'ttmessage' => "Chúc mừng bạn nhận một Vourcher",
            'ttemail' => '<EMAIL>',
            'ttphone' => '0906009618',
            'ttfullname' => 'Nguyễn Đức Ninh',
            'card_id' => 2203,
            'dataBuy' => json_encode($dataBuy),
            'access_urbox'=>'********************************',
            'agent_site'=>34,
            'debug'=>1
        );
        $curl_handle = curl_init('https://api.urbox.vn/2.0/cart/cartApp');
        curl_setopt($curl_handle, CURLOPT_HEADER, 0);
        curl_setopt($curl_handle, CURLOPT_VERBOSE, 0);
        curl_setopt($curl_handle, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl_handle, CURLOPT_USERAGENT, "Mozilla/6.1 (compatible;)");
        curl_setopt($curl_handle, CURLOPT_POST, true);
        curl_setopt($curl_handle, CURLOPT_POSTFIELDS, $post);
        curl_setopt($curl_handle, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl_handle, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl_handle, CURLOPT_CONNECTTIMEOUT, 240);
        curl_setopt($curl_handle, CURLOPT_TIMEOUT, 360);
        $ex = curl_exec($curl_handle);
        $response = json_decode($ex,true);

        return System::apiSuccess($response);*/
    }

    function mbalWinningCardBalace(){
        if(!FunctionLib::redisFlag('mbalWinningCardBalace',1)){
            echo 'Da call';die;
        }else {
            $app_id = 255;
            $app_secret = '94e4ec53b6856fdeaff596533f46bb2b8db8dc2c';
            $re = DB::query("select * FROM draw where app_id=".$app_id." and process=3 AND status = 2 order by id ASC  limit 5");
            $msg = '';
            if($re){
                $genkeyCache = md5('DRAWGIFT'.$app_id);
                $drawGift = CacheLib::get($genkeyCache);
                if (empty($dataReturn)) {
                    $drawGift = DB::fetch_all("SELECT id,app_id,gift_detail_id,value FROM draw_gift WHERE app_id=".$app_id." AND status=2");
                    CacheLib::set($genkeyCache, $drawGift, 30 * 60 * 60);
                }
                while ($row = @mysql_fetch_assoc($re)){
                    $dataBuy = array();
                    if(!empty($drawGift)&&isset($drawGift[$row['win_draw_gift_id']])){
                        $dataBuy[] = array('priceId'=> $drawGift[$row['win_draw_gift_id']]['gift_detail_id'],'quantity'=>1,'amount'=> $drawGift[$row['win_draw_gift_id']]['value']);
                        $curl = new CURL();
                        $post = array(
                            'ttphone' => $row['phone'],
                            'ttfullname' => $row['fullname'],
                            'transaction_id' =>'MBAL-AgeasLife-2020-BALACE-'. $row['id'],
                            'app_id' => $app_id,
                            'app_secret' => $app_secret,
                            'dataBuy' => json_encode($dataBuy)
                        );
                        $ex = $curl->post('http://api.urbox.internal/2.0/cart/cartPayVoucher',$post);
                        $response = json_decode($ex,true);
                        $update = array();
                        if($response['done']==1){
                            $update['process'] = 5;
                            $update['cart_id'] = $response['data']['cart']['id'];
                        }else{
                            $update['process'] = 7;
                        }
                        DB::update('draw',$update,"id='".$row['id']."'");
                        $msg .=  $response['msg'].'<br/>';
                    }

                }
            }
            FunctionLib::redisFlag('mbalWinningCardBalace', 2);
            return System::apiSuccess($msg);
        }
    }

    function mbalWinningCardBalaceSendSMS(){
        if(!FunctionLib::redisFlag('mbalWinningCardBalaceSendSMS',1)){
            echo 'Da call';die;
        }else {
            $app_id = 255;
            $re = DB::query("select * FROM draw where  app_id=".$app_id." and process=5 AND status = 2 order by id ASC limit 5");
            $msg = '';
            if ($re) {
                $data = array();
                $cart_id = array();
                while ($row = @mysql_fetch_assoc($re)) {
                    $data[$row['id']] = $row;
                    $cart_id[$row['cart_id']] = $row['cart_id'];
                }

                $giftCode = array();
                $card = array();
                $cardToken = array();
                if(!empty($cart_id)) {
                    $reg = DB::query("SELECT * FROM ".T_GIFT_CODE." WHERE cart_id IN (".implode(',',$cart_id).") AND status = 1");
                    if($reg) {
                        while ($rowg = @mysql_fetch_assoc($reg)) {
                            $giftCode[$rowg['cart_id']] = $rowg;
                            $cardToken[$rowg['code']] = $rowg['code'];
                        }
                        if(!empty($cardToken)){
                            $rec = DB::query("SELECT id,number,token FROM ".T_CARD." WHERE token IN ('".implode("','",$cardToken)."')");
                            if($rec) {
                                while ($rowc = @mysql_fetch_assoc($rec)) {
                                    $card[$rowc['token']] = $rowc;
                                }
                            }
                        }
                    }

                }
                if(!empty($data) && !empty($giftCode) && !empty($card)){
                    $genkeyCache = md5('DRAWGIFT'.$app_id);
                    $drawGift = CacheLib::get($genkeyCache);
                    if (empty($dataReturn)) {
                        $drawGift = DB::fetch_all("SELECT id,app_id,gift_detail_id,value FROM draw_gift WHERE app_id=".$app_id." AND status=2");
                        CacheLib::set($genkeyCache, $drawGift, 30 * 60 * 60);
                    }
                    foreach ($data as $dt){
                        if(isset($giftCode[$dt['cart_id']])){
                            if(isset($card[$giftCode[$dt['cart_id']]['code']])){
                                $number = $card[$giftCode[$dt['cart_id']]['code']]['number'];
                                $pin = $giftCode[$dt['cart_id']]['pin'];
                                $curl = new CURL();
                                $linkService = 'https://api-02.worldsms.vn/webapi/sendSMS';
                                $headers = array(
                                    'Content-type:application/json',
                                    'Authorization: Basic ' . 'YmFuY2FzX3VyYm94OmZFbnZHN3BGQ0Y='
                                );
                                $optionAuthen = [['k' => CURLOPT_HTTPHEADER, 'v' => $headers]];

                                $dataPost = array(
                                    "from" => "MBAgeasLife",
                                    "to" => $dt['phone'],
                                    "text" => "Chuc mung QK da nhan duoc giai thuong la: 1 the qua tang UrBox tri gia ".$drawGift[$dt['win_draw_gift_id']]['value']." VND tu MB Ageas Life.  Ma the qua tang ".$number." và ma PIN: ".$pin." . Vui long truy cap: urbox.vn/download.html de download va dang ky App UrBox. Trung tam DVKH se lien lac voi KH de ho tro kich hoat, su dung voucher. Hotline: 1900 299 232",
                                );
                                $curlMB = $curl->doRequest('POST', $linkService, json_encode($dataPost), $optionAuthen);
                                $update['data'] = $curlMB;
                                $update['process'] = 6;
                                if($curlMB['status'] == 0){
                                    $update['process'] = 8;
                                }
                                DB::update('draw', $update, "id='" . $dt['id'] . "'");
                            }
                        }
                    }
                }
            }
            FunctionLib::redisFlag('mbalWinningCardBalaceSendSMS', 2);
            return System::apiSuccess($msg);
        }

    }

    function mbalWinningCardPhone(){
        if(!FunctionLib::redisFlag('mbalWinningCardPhone',1)){
            echo 'Da call';die;
        }else {
            $app_id = 251;
            $app_secret = 'efb88646d286b81faf2f6e5c72eb143cfa34833f';
            $re = DB::query("select * FROM draw where app_id=".$app_id." and process=3 AND status = 2 order by id ASC  limit 5");
            $msg = '';
            if($re){
                #Nhà mạng khách dùng 1: Viettel, 2: Vinaphone, 3: Mobifone, 4: Vietnammobie
                $giftMobile = array(
                    34 => array(#30k
                        1 => 4547,#VT
                        2 => 4549,#VN
                        3 => 4548,#MB
                    ),
                    35 => array(#50k
                        1 => 4550,
                        2 => 4552,
                        3 => 4551
                    )
                );
                while ($row = @mysql_fetch_assoc($re)){
                    $giftMap = isset($giftMobile[$row['win_draw_gift_id']])?$giftMobile[$row['win_draw_gift_id']]:array();
                    $dataBuy = array();
                    if(!empty($giftMap)){
                        $dataBuy[] = array('priceId'=> $giftMap[$row['network']],'quantity'=>1,'amount'=> ($row['win_draw_gift_id']==24?30000:50000));
                        $curl = new CURL();
                        $post = array(
                            'ttphone' => $row['phone'],
                            'ttfullname' => $row['fullname'],
                            'callback_transaction' => 1,
                            'transaction_id' =>'MBAL-AgeasLife-2020-'. $row['id'],
                            'app_id' => $app_id,
                            'app_secret' => $app_secret,
                            'dataBuy' => json_encode($dataBuy)
                        );
                        $ex = $curl->post('http://api.urbox.internal/2.0/cart/cartPayVoucher',$post);
                        $response = json_decode($ex,true);
                        $update = array();
                        if($response['done']==1){
                            $update['process'] = 5;
                            $update['cart_id'] = $response['data']['cart']['id'];
                        }else{
                            $update['process'] = 7;
                        }
                        DB::update('draw',$update,"id='".$row['id']."'");
                        $msg .=  $response['msg'].'<br/>';
                    }else{
                        DB::update('draw', array('process'=>7), "id='" . $row['id'] . "'");
                        $msg .=  'Không tìm thấy quà<br/>';
                    }

                }
            }
            FunctionLib::redisFlag('mbalWinningCardPhone', 2);
            return System::apiSuccess($msg);
        }
    }

    function mbalSendSmsContent(){
        if(!FunctionLib::redisFlag('mbalSendSmsContent',1)){
            echo 'Da call';die;
        }else {
            $app_id = Url::getParamInt('app_id',255);
            $auth = Url::getParam('auth','YmFuY2FzX3VyYm94OmZFbnZHN3BGQ0Y=');
            $re = DB::query("select * FROM draw where  app_id=".$app_id." and process=9 AND status = 2 order by id ASC limit 10");
            $msg = '';
            if ($re) {
                while ($row = @mysql_fetch_assoc($re)) {
                    $curl = new CURL();
                    $linkService = 'https://api-02.worldsms.vn/webapi/sendSMS';
                    $headers = array(
                        'Content-type:application/json',
                        'Authorization: Basic ' . $auth
                    );
                    $optionAuthen = [['k' => CURLOPT_HTTPHEADER, 'v' => $headers]];
                    $dataPost = array(
                        "from" => "MBAgeasLife",
                        "to" => $row['phone'],
                        "text" => $row['content']
                    );
                    $postCURL = $curl->doRequest('POST', $linkService, json_encode($dataPost), $optionAuthen);
                    $json = json_decode($postCURL,true);
                    $update = array();
                    $update['data'] = $postCURL;
                    $update['process'] = 10;
                    if($json['status'] == 0){
                        $update['process'] = 11;
                    }
                    DB::update('draw',$update, "id='" . $row['id'] . "'");
                }
            }
            FunctionLib::redisFlag('mbalSendSmsContent', 2);
            return System::apiSuccess($msg);
        }

    }

    function mbalWinningCardPhoneSms(){
        if(!FunctionLib::redisFlag('mbalWinningCardPhoneSms',1)){
            echo 'Da call';die;
        }else {
            $re = DB::query("select * FROM draw where app_id=251 and process=5 AND status = 2 order by id ASC limit 5");
            $msg = '';
            if ($re) {
                $data = array();
                $cart_id = array();
                while ($row = @mysql_fetch_assoc($re)) {
                    $data[$row['id']] = $row;
                    $cart_id[$row['cart_id']] = $row['cart_id'];
                }

                $giftCode = array();
                if(!empty($cart_id)) {
                    $reg = DB::query("SELECT * FROM ".T_GIFT_CODE." WHERE cart_id IN (".implode(',',$cart_id).") AND status = 1");
                    if($reg) {
                        while ($rowg = @mysql_fetch_assoc($reg)) {
                            $giftCode[$rowg['cart_id']] = System::decrypt($rowg['code']);
                        }
                    }

                }
                if(!empty($data) && !empty($giftCode)){
                    foreach ($data as $dt){
                        if(isset($giftCode[$dt['cart_id']])){
                            $curl = new CURL();
                            $linkService = 'https://api-02.worldsms.vn/webapi/sendSMS';
                            $headers = array(
                                'Content-type:application/json',
                                'Authorization: Basic ' . 'YmFuY2FzX3VyYm94OmZFbnZHN3BGQ0Y='
                            );
                            $optionAuthen = [['k' => CURLOPT_HTTPHEADER, 'v' => $headers]];
                            $network = '';
                            if($dt['network'] == 1){
                                $network = 'Viettel';
                            }
                            if($dt['network'] == 2){
                                $network = 'Vinaphone';
                            }
                            if($dt['network'] == 3){
                                $network = 'Mobifone';
                            }
                            $dataPost = array(
                                "from" => "MBAgeasLife",
                                "to" => $dt['phone'],
                                "text" => "MB Ageas Life (MBAL) tran trong cam on QK da lua chon su dung san pham dich vu. MBAL tran trong gui QK ma nap the dien thoai ". $network ." ". $giftCode[$dt['cart_id']]." menh gia ".($dt['win_draw_gift_id']==34?30:50).".000 VND Chuc QK nhieu suc khoe va thanh cong. Hotline 1900299232",
                            );
                            $curl->doRequest('POST', $linkService, json_encode($dataPost), $optionAuthen);
                            DB::update('draw', array('process' => 6), "id='" . $dt['id'] . "'");
                        }
                    }
                }
            }
            FunctionLib::redisFlag('mbalWinningCardPhoneSms', 2);
            return System::apiSuccess($msg);
        }

    }

    function mbal(){
        $re = DB::query("select * FROM draw where app_id=81 and process=3 order by rand() limit 5");
        $msg = '';
        if ($re) {
            #Nhà mạng khách dùng 1: Viettel, 2: Vinaphone, 3: Mobifone, 4: Vietnammobie
            $giftMobile = array(
                24 => array(#30k
                    1 => 3037,#VT
                    3 => 3042,#MB
                    2 => 3047,#VN
                    4 => 3143,#VNM
                ),
                25 => array(#50k
                    1 => 3039,
                    3 => 3044,
                    2 => 3049,
                    4 => 3144
                )
            );
            while ($row = @mysql_fetch_assoc($re)) {
                $giftMap = isset($giftMobile[$row['win_draw_gift_id']]) ? $giftMobile[$row['win_draw_gift_id']] : array();
                $dataBuy = array();
                if (!empty($giftMap)) {
                    $dataBuy[] = array('priceId' => $giftMap[$row['network']], 'quantity' => 1, 'amount' => ($row['win_draw_gift_id'] == 24 ? 30000 : 50000));
                    $curl = new CURL();
                    $post = array(
                        'ttphone' => $row['phone'],
                        'ttfullname' => $row['fullname'],
                        'callback_transaction' => 1,
                        'transaction_id' => 'MBAL-' . $row['id'],
                        'app_id' => 81,
                        'app_secret' => 'd21b0be76608883fca599ef1195d60d5bd605f68',
                        'dataBuy' => json_encode($dataBuy)
                    );

                    $ex = $curl->post('http://api.urbox.internal/2.0/cart/cartPayVoucher',$post);
                    $response = json_decode($ex,true);

                    if ($response['done'] == 1) {
                        DB::update('draw', array('process' => 5), "id='" . $row['id'] . "'");
                    }
                    $msg .= $response['msg'] . '<br/>';
                }

            }
        }
        return System::apiSuccess($msg);

    }

    function mbalSms()
    {
        $re = DB::query("select * FROM draw where app_id=81 and process=5 order by rand() limit 5");
        $msg = '';
        if ($re) {
            while ($row = @mysql_fetch_assoc($re)) {
                $curl = new CURL();
                $linkService = 'https://api-02.worldsms.vn/webapi/sendSMS';
                $headers = array(
                    'Content-type:application/json',
                    'Authorization: Basic ' . 'YmFuY2FzX3VyYm94OmZFbnZHN3BGQ0Y='
                );
                $optionAuthen = [['k' => CURLOPT_HTTPHEADER, 'v' => $headers]];
                $dataPost = array(
                    "from" => "MBAgeasLife",
                    "to" => $row['phone'],
                    "text" => "Chuc mung QK da duoc nap the dien thoai ".($row['win_draw_gift_id']==24?30:50).".000 khi phat hanh thanh cong Hop dong bao hiem MB Ageas Life. QK vui long kiem tra tai khoan. Hotline: 1900299232",
                    /*"unicode" => 0,
                    "dlr" => 0,
                    "smsid" => 0,
                    "Campaignid/messageid" => 0,*/
                );

                $curl->doRequest('POST', $linkService, json_encode($dataPost), $optionAuthen);
                DB::update('draw', array('process' => 6), "id='" . $row['id'] . "'");
            }
        }
        return System::apiSuccess($msg);

    }

    function topupIO()
    {
        $limit = Url::getParamInt('limit', 20);
        $re = DB::query("select * FROM " . T_ORDER_TOPUP . " where status=" . IS_ON . " and pointu_id=0 and process=2 and plan_time<=" . TIME_NOW . " order by rand() limit " . $limit);
        $msg = '';
        if ($re) {
            #Nhà mạng khách dùng 1: Viettel, 2: Vinaphone, 3: Mobifone, 4: Vietnammobie
            $giftMobile = array(
                1 => 3126,#VT
                2 => 3128,#VN
                3 => 3127,#MB
                4 => 3142,#VNM
            );
            $order_topup = array();
            $appID = array();
            while ($row = @mysql_fetch_assoc($re)) {
                if ($row['app_id'] > 0) {
                    $appID[$row['app_id']] = $row['app_id'];
                    $order_topup[$row['id']] = $row;
                }
            }
            $app = array();
            if (!empty($appID)) {
                $app = DB::fetch_all("SELECT * FROM " . T_APP . " WHERE id IN (" . implode(',', $appID) . ")");
            }
            if (!empty($app) && !empty($order_topup)) {
                foreach ($order_topup as $topup) {
                    if (isset($app[$topup['app_id']])) {
                        $dataBuy = array();
                        $dataBuy[] = array('priceId' => $giftMobile[$topup['network']], 'quantity' => 1, 'amount' => $topup['money']);
                        $curl = new CURL();
                        $post = array(
                            'ttphone' => $topup['phone'],
                            'ttfullname' => $topup['fullname'],
                            'callback_transaction' => 1,
                            'transaction_id' => 'ORDERTOPUP-' . $topup['id'],
                            'app_id' => $topup['app_id'],
                            'app_secret' => $app[$topup['app_id']]['access_key'],
                            'dataBuy' => json_encode($dataBuy)
                        );

                        $ex = $curl->post('http://api.urbox.internal/2.0/cart/cartPayVoucher',$post);
                        $response = json_decode($ex,true);
                        $arrUpdate['sent_time'] = TIME_NOW;
                        if ($response['done'] == 1) {
                            $arrUpdate['process'] = 3;
                            $arrUpdate['cart_id'] = $response['data']['cart']['id'];
                            $cart_detail = $response['data']['cart']['code_link_gift'];
                            $arrUpdate['cart_detail_id'] = $cart_detail[0]['cart_detail_id'];
                        } else {
                            $arrUpdate['process'] = 4;
                        }
                        DB::update(T_ORDER_TOPUP, $arrUpdate, 'id=' . $topup['id']);
                        $msg .= $response['msg'] . '<br/>';
                    }

                }
            }
        }
        return System::apiSuccess($msg);

    }

    function ioProcess()
    {
        $limit = Url::getParamInt('limit', 200);
        $re = DB::query("select * FROM " . T_ORDER_TOPUP . " where status=" . IS_ON . " and cart_id>0 and process=3 and hash='' order by rand() limit " . $limit);
        $msg = '';
        if ($re) {
            $order_topup = array();
            $arrCart = array();
            while ($row = @mysql_fetch_assoc($re)) {
                $order_topup[$row['cart_id']] = $row;
                $arrCart[$row['cart_id']] = $row['cart_id'];
                DB::update(T_ORDER_TOPUP, array('hash' => 1), 'id=' . $row['id']);
            }
            if (!empty($arrCart)) {
                $rec = DB::query("select * FROM " . T_CART . " where id IN (" . implode(',', array_keys($arrCart)) . ")");
                if ($rec) {
                    while ($rowc = @mysql_fetch_assoc($rec)) {
                        if (isset($order_topup[$rowc['id']]) && ($rowc['status'] != 2 || $rowc['pay_status'] != 2)) {
                            DB::update(T_ORDER_TOPUP, array('process' => 4), 'id=' . $order_topup[$rowc['id']]['id']);
                        }
                    }
                }
            }

        }
        return System::apiSuccess($msg);
    }

    function hanwhaWinningCardBalace()
    {

        if (!FunctionLib::redisFlag('hanwhaWinningCardBalace', 1)) {
            echo 'Da call';
            die;
        } else {
            $app_id = Url::getParamInt('app_id',500000116);
            $app_secret = Url::getParam('app_secret','580bc51325666708550a1ed73d530aa5');
            $re = DB::query("select * FROM draw where app_id=" . $app_id . " and process=3 AND status = 2 order by id ASC  limit 5");
            $msg = '';
            if ($re) {
                $genkeyCache = md5('DRAWGIFT' . $app_id);
                $drawGift = CacheLib::get($genkeyCache);
                if (empty($dataReturn)) {
                    $drawGift = DB::fetch_all("SELECT id,app_id,gift_detail_id,value FROM draw_gift WHERE app_id=" . $app_id . " AND status=2");
                    CacheLib::set($genkeyCache, $drawGift, 30 * 60 * 60);
                }
                while ($row = @mysql_fetch_assoc($re)) {
                    $dataBuy = array();
                    if (!empty($drawGift) && isset($drawGift[$row['win_draw_gift_id']])) {
                        $dataBuy[] = array('priceId' => $drawGift[$row['win_draw_gift_id']]['gift_detail_id'], 'quantity' => 1, 'amount' => $row['amount']);
                        $curl = new CURL();
                        $post = array(
                            'ttphone' => $row['phone'],
                            'ttfullname' => $row['fullname'],
                            'transaction_id' => 'HANWHA-2021-BALACE-' . $row['id'],
                            'app_id' => $app_id,
                            'app_secret' => $app_secret,
                            'dataBuy' => json_encode($dataBuy)
                        );
                        $ex = $curl->post('https://api.urbox.vn/2.0/cart/cartPayVoucher', $post);
                        $response = json_decode($ex, true);
                        $update = array();
                        if ($response['done'] == 1) {
                            $update['process'] = 5;
                            $update['cart_id'] = $response['data']['cart']['id'];
                        } else {
                            $update['process'] = 7;
                        }
                        DB::update('draw', $update, "id='" . $row['id'] . "'");
                        $msg .= $response['msg'] . '<br/>';
                    }

                }
            }
            FunctionLib::redisFlag('hanwhaWinningCardBalace', 2);
            return System::apiSuccess($msg);
        }
    }

    function hanwhaWinningCardBalaceSendSMS()
    {
        if (!FunctionLib::redisFlag('hanwhaWinningCardBalaceSendSMS', 1)) {
            echo 'Da call';
            die;
        } else {
            $app_id = Url::getParamInt('app_id',500000116);
            $re = DB::query("select * FROM draw where  app_id=" . $app_id . " and process=5 AND status = 2 order by id ASC limit 5");
            $msg = '';
            if ($re) {
                $data = array();
                $cart_id = array();
                while ($row = @mysql_fetch_assoc($re)) {
                    $data[$row['id']] = $row;
                    $cart_id[$row['cart_id']] = $row['cart_id'];
                }

                $giftCode = array();
                $card = array();
                $cardToken = array();
                if (!empty($cart_id)) {
                    $reg = DB::query("SELECT * FROM " . T_GIFT_CODE . " WHERE cart_id IN (" . implode(',', $cart_id) . ") AND status = 1");
                    if ($reg) {
                        while ($rowg = @mysql_fetch_assoc($reg)) {
                            $giftCode[$rowg['cart_id']] = $rowg;
                            $cardToken[$rowg['code']] = $rowg['code'];
                        }
                        if (!empty($cardToken)) {
                            $rec = DB::query("SELECT id,number,token FROM " . T_CARD . " WHERE token IN ('" . implode("','", $cardToken) . "')");
                            if ($rec) {
                                while ($rowc = @mysql_fetch_assoc($rec)) {
                                    $card[$rowc['token']] = $rowc;
                                }
                            }
                        }
                    }

                }
                if (!empty($data) && !empty($giftCode) && !empty($card)) {
                    $genkeyCache = md5('DRAWGIFT' . $app_id);
                    /*$drawGift = CacheLib::get($genkeyCache);
                    if (empty($dataReturn)) {
                        $drawGift = DB::fetch_all("SELECT id,app_id,gift_detail_id,value FROM draw_gift WHERE app_id=" . $app_id . " AND status=2");
                        CacheLib::set($genkeyCache, $drawGift, 30 * 60 * 60);
                    }*/
                    foreach ($data as $dt) {
                        if (isset($giftCode[$dt['cart_id']])) {
                            if (isset($card[$giftCode[$dt['cart_id']]['code']])) {
                                $number = $card[$giftCode[$dt['cart_id']]['code']]['number'];
                                $pin = $giftCode[$dt['cart_id']]['pin'];
                                $token = System::decrypt($card[$giftCode[$dt['cart_id']]['code']]['token']);
                                $link = Shorten::build('https://page.urbox.vn/card/'.$token);
                                $content = 'Hanwha Life gui den A/C Qua tang Urbox '.$dt['content'].'. Vui long nhan vao '.$link.' de su dung. Hotline: 1900299232';
                                if(in_array($app_id,array(328,341,335,340,343))){
                                    $content = 'Hanwha Life gui den '.$dt['fullname'].' Qua tang Urbox '.$dt['content'].'. Truy cap '.$link.' va nhap ma PIN '.$pin.' de su dung. Hotline: 1900299232';
                                }
                                $phone = '84'.substr($dt['phone'],1,9);
                                $curlHW = self::sendSmsHanwha($content,$phone);
                                $update['data'] = $curlHW;
                                $update['process'] = 6;
                                $curlHWJson = json_decode($curlHW,true);
                                if ($curlHWJson['code'] != 0) {
                                    $update['process'] = 8;
                                }
                                DB::update('draw', $update, "id='" . $dt['id'] . "'");
                            }
                        }
                    }
                }
            }
            FunctionLib::redisFlag('hanwhaWinningCardBalaceSendSMS', 2);
            return System::apiSuccess($msg);
        }

    }

    function hanwhaSendSMS()
    {
        if (!FunctionLib::redisFlag('hanwhaSendSMS', 1)) {
            echo 'Da call';
            die;
        } else {
            $app_id = Url::getParamInt('app_id',500000116);
            $re = DB::query("select * FROM draw where  app_id=" . $app_id . " and process=9 AND status = 2 order by id ASC limit 5");
            $msg = '';
            if ($re) {
                while ($row = @mysql_fetch_assoc($re)) {
                    $phone = '84'.substr($row['phone'],1,9);
                    $curlHW = self::sendSmsHanwha($row['content'],$phone);
                    $update['data'] = $curlHW;
                    $update['process'] = 10;
                    $curlHWJson = json_decode($curlHW,true);
                    if ($curlHWJson['code'] != 0) {
                        $update['process'] = 11;
                    }
                    DB::update('draw', $update, "id='" . $row['id'] . "'");
                }
            }
            FunctionLib::redisFlag('hanwhaSendSMS', 2);
            return System::apiSuccess($msg);
        }

    }

    function sendSmsHanwha($content,$phone){
        $jwt_key = "nfn5Da9AM8B8DDYwpLTwlYnScgJCPKEU";
        $checksum_key = '388adac4-22a5-43ad-98c1-7a583a238664';
        $url = 'https://api.hanwhalife.com.vn/zms.sms/sms/v1/Send';
        $jwt_payload = [
            "iss" => "HanwhaLife",
            "aud" => "Urbox",
            "providerKey" => "Urbox",
            "nbf" => time() -100,
            "exp" => time()-100+ 86400,
            "iat" => time()-100
        ];

        #chay thu xem
        $body = [
            'dept'=>'URBOX',
            'content' => $content,
            'phone' => $phone,
            'trackingId' => (string)(time().uniqid())
        ];
        $jwt_token = \Firebase\JWT\JWT::encode( $jwt_payload, $jwt_key );
        $checksum = md5( json_encode( $body ) . $checksum_key );
        $headers = array(
            'accept:application/json',
            'Content-type:application/json',
            'Authorization: '.$jwt_token
        );
        $curl = new CURL();
        $optionAuthen = [['k' => CURLOPT_HTTPHEADER, 'v' => $headers]];
        $curlHW = $curl->doRequest('POST', $url.'?checksum='.$checksum, json_encode($body), $optionAuthen);
        return $curlHW;
    }

    function mbalGenCardBalace()
    {

        if (!FunctionLib::redisFlag('mbalGenCardBalace', 1)) {
            echo 'Da call';
            die;
        } else {
            $app_id = Url::getParamInt('app_id',500000178);
            $app_secret = Url::getParam('app_secret','6ec290fc61c6cfa72e63b9fa54a6f140');
            $re = DB::query("select * FROM draw where app_id=" . $app_id . " and process=3 AND status = 2 order by id ASC  limit 5");
            $msg = '';
            if ($re) {
                $genkeyCache = md5('DRAWGIFT' . $app_id);
                $drawGift = CacheLib::get($genkeyCache);
                if (empty($dataReturn)) {
                    $drawGift = DB::fetch_all("SELECT id,app_id,gift_detail_id,value FROM draw_gift WHERE app_id=" . $app_id . " AND status=2");
                    CacheLib::set($genkeyCache, $drawGift, 30 * 60 * 60);
                }
                while ($row = @mysql_fetch_assoc($re)) {
                    $dataBuy = array();
                    if (!empty($drawGift) && isset($drawGift[$row['win_draw_gift_id']])) {
                        $dataBuy[] = array('priceId' => $drawGift[$row['win_draw_gift_id']]['gift_detail_id'], 'quantity' => 1, 'amount' => $row['amount']);
                        $curl = new CURL();
                        $post = array(
                            'ttphone' => $row['phone'],
                            'ttfullname' => $row['fullname'],
                            'transaction_id' => (($app_id==389)?'MBAL-2021-BALACE-':'MBAL-'.$app_id.'-2022-BALACE-') . $row['id'],
                            'app_id' => $app_id,
                            'app_secret' => $app_secret,
                            'dataBuy' => json_encode($dataBuy)
                        );
                        $ex = $curl->post('https://api.urbox.vn/2.0/cart/cartPayVoucher', $post);
                        $response = json_decode($ex, true);
                        $update = array();
                        if ($response['done'] == 1) {
                            $update['process'] = 5;
                            $update['cart_id'] = $response['data']['cart']['id'];
                        } else {
                            $update['process'] = 7;
                        }
                        DB::update('draw', $update, "id='" . $row['id'] . "'");
                        $msg .= $response['msg'] . '<br/>';
                    }

                }
            }
            FunctionLib::redisFlag('mbalGenCardBalace', 2);
            return System::apiSuccess($msg);
        }
    }

    function mbalSendSmsPush()
    {
        if (!FunctionLib::redisFlag('mbalSendSmsPush', 1)) {
            echo 'Da call';
            die;
        } else {
            $app_id = Url::getParamInt('app_id',500000178);
            $re = DB::query("select * FROM draw where app_id=" . $app_id . " and process=5 AND status = 2 order by id ASC limit 5");
            $msg = '';
            if ($re) {
                $data = array();
                $cart_id = array();
                while ($row = @mysql_fetch_assoc($re)) {
                    $data[$row['id']] = $row;
                    $cart_id[$row['cart_id']] = $row['cart_id'];
                }

                $giftCode = array();
                $card = array();
                $cardToken = array();
                if (!empty($cart_id)) {
                    $reg = DB::query("SELECT * FROM " . T_GIFT_CODE . " WHERE cart_id IN (" . implode(',', $cart_id) . ") AND status = 1");
                    if ($reg) {
                        while ($rowg = @mysql_fetch_assoc($reg)) {
                            $giftCode[$rowg['cart_id']] = $rowg;
                            $cardToken[$rowg['code']] = $rowg['code'];
                        }
                        if (!empty($cardToken)) {
                            $rec = DB::query("SELECT id,number,token FROM " . T_CARD . " WHERE token IN ('" . implode("','", $cardToken) . "')");
                            if ($rec) {
                                while ($rowc = @mysql_fetch_assoc($rec)) {
                                    $card[$rowc['token']] = $rowc;
                                }
                            }
                        }
                    }

                }
                if (!empty($data) && !empty($giftCode) && !empty($card)) {
                    foreach ($data as $dt) {
                        if (isset($giftCode[$dt['cart_id']])) {
                            if (isset($card[$giftCode[$dt['cart_id']]['code']])) {
                                $number = $card[$giftCode[$dt['cart_id']]['code']]['number'];
                                $pin = $giftCode[$dt['cart_id']]['pin'];
                                $expired = $giftCode[$dt['cart_id']]['expired'];
                                #$token = System::decrypt($card[$giftCode[$dt['cart_id']]['code']]['token']);
                                #$link = Shorten::build('https://page.urbox.dev/card/'.$token);
                                $content = 'Chuc mung QK da nhan duoc giai thuong la: 1 the qua tang UrBox tri gia '.FunctionLib::numberFormat($dt['amount']).'d tu MB Ageas Life. Ma the qua tang '.$number.' va ma PIN '.$pin.' Vui long truy cap: urbox.vn/download.html de download va dang ky App UrBox. HSD '.($expired>0?FunctionLib::dateFormat($expired,'d/m/Y'):' vô hạn').' Hotline: 1900299232';
                                #$content = 'Chuc mung QK da nhan duoc Qua tang tu CTKM "Noi binh an  - Vang day tui" cua MB Ageas Life: 01 E- Voucher/01 E-Voucher Vang tri gia '.FunctionLib::numberFormat($dt['amount']).'VND. Ma the qua tang '.$number.' va ma PIN '.$pin.' Vui long truy cap: urbox.vn/download.html de download va dang ky App UrBox. HSD '.($expired>0?FunctionLib::dateFormat($expired,'d/m/Y'):' vô hạn').' Hotline: 1900299232';
                                $phone = $dt['phone'];
                                $curlHW = self::sendSmsMBal($content,$phone,$stringBody,$app_id);
                                $update['data'] = $curlHW.$stringBody;
                                $update['process'] = 6;
                                $curlHWJson = json_decode($curlHW,true);
                                if ($curlHWJson['code'] != 0) {
                                    $update['process'] = 8;
                                }
                                DB::update('draw', $update, "id='" . $dt['id'] . "'");
                            }
                        }
                    }
                }
            }
            FunctionLib::redisFlag('mbalSendSmsPush', 2);
            return System::apiSuccess($msg);
        }

    }

    function sendSmsMBal($content = '',$phone = '',&$stringBody = '',$app_id = 0){
        $curl = new CURL();
        $linkService = 'https://api-02.worldsms.vn/webapi/sendSMS';
        $authorization = 'YmFuY2FzX3VyYm94OmZFbnZHN3BGQ0Y=';
        if($app_id == 683){
            $authorization = 'bWJhbF9hZ2VuY3lhcGk6NDdRcWJSNk4=';
        }else if($app_id == 209){
            $authorization = 'bWJhbGN4Onh6RFp1cEJk';
        }
        $headers = array(
            'Content-type:application/json',
            'Authorization: Basic '. $authorization
        );
        $optionAuthen = [['k'=>CURLOPT_HTTPHEADER,'v'=>$headers]];
        $dataPost = array(
            "from" => "MBAgeasLife",
            "to" => $phone,
            "text" => $content
        );

        $curlHW = $curl->doRequest('POST', $linkService, json_encode($dataPost),$optionAuthen);
        $stringBody = json_encode($dataPost).json_encode($optionAuthen);
        return $curlHW;
    }

    function sendSmsFWS($content = '',$phone = '',&$stringBody = '')
    {
        $body = [
            "from" => "UrBox",
            'phoneNumber' => $phone,
            'email' => "",
            "fullname" => "",
            "voucher_url" => "",
            "amount" => 0,
            "email_content" => "",
            "title_email" => "",
            "sms_content" => StringLib::stripUnicode($content),
            "client_id" => ""
        ];

        $linkApi = 'https://portal.fwd.com.vn/fwd-api/urbox/sms-email';
        $headers = [
            'Content-type:application/json',
            "Accept" => "application/json"
        ];
        $curl = new CURL();
        $optionAuthen = [
            ['k'=>CURLOPT_HTTPHEADER,'v'=>$headers],
            ['k'=>CURLOPT_USERPWD,'v'=>'fwd-api:P@sswordFwd2020'],
        ];
        $curlHW = $curl->doRequest('POST', $linkApi, json_encode($body),$optionAuthen);
        $stringBody = json_encode($body);
        return $curlHW;

    }

    function fwdGenCardBalace()
    {

        if (!FunctionLib::redisFlag('fwdGenCardBalace', 1)) {
            echo 'Da call';
            die;
        } else {
            $app_id = Url::getParamInt('app_id',403);
            $app_secret = Url::getParam('app_secret','e190365d2f1b885bd76f48452c6273e2');
            $re = DB::query("select * FROM draw where status = 2 AND app_id=" . $app_id . " and process=3 order by id ASC  limit 5");
            $msg = '';
            if ($re) {
                $genkeyCache = md5('DRAWGIFT' . $app_id);
                $drawGift = CacheLib::get($genkeyCache);
                if (empty($dataReturn)) {
                    $drawGift = DB::fetch_all("SELECT id,app_id,gift_detail_id,value FROM draw_gift WHERE app_id=" . $app_id . " AND status=2");
                    CacheLib::set($genkeyCache, $drawGift, 30 * 60 * 60);
                }
                while ($row = @mysql_fetch_assoc($re)) {
                    $dataBuy = array();
                    if (!empty($drawGift) && isset($drawGift[$row['win_draw_gift_id']])) {
                        $dataBuy[] = array('priceId' => $drawGift[$row['win_draw_gift_id']]['gift_detail_id'], 'quantity' => 1, 'amount' => $row['amount']);
                        $curl = new CURL();
                        $post = array(
                            'ttphone' => $row['phone'],
                            'ttfullname' => $row['fullname'],
                            'transaction_id' => 'FWD-2021-BALACE-' . $row['id'],
                            'app_id' => $app_id,
                            'app_secret' => $app_secret,
                            'dataBuy' => json_encode($dataBuy)
                        );
                        $ex = $curl->post('https://api.urbox.vn/2.0/cart/cartPayVoucher', $post);
                        $response = json_decode($ex, true);
                        $update = array();
                        if ($response['done'] == 1) {
                            $update['process'] = 5;
                            $update['cart_id'] = $response['data']['cart']['id'];
                        } else {
                            $update['process'] = 7;
                        }
                        DB::update('draw', $update, "id='" . $row['id'] . "'");
                        $msg .= $response['msg'] . '<br/>';
                    }

                }
            }
            FunctionLib::redisFlag('fwdGenCardBalace', 2);
            return System::apiSuccess($msg);
        }
    }

    function fwdSendSmsPush()
    {
        if (!FunctionLib::redisFlag('fwdSendSmsPush', 1)) {
            echo 'Da call';
            die;
        } else {
            $app_id = Url::getParamInt('app_id',500000181);
            $re = DB::query("select * FROM draw where app_id=" . $app_id . " and process=5 AND status = 2 order by id ASC limit 5");
            $msg = '';
            $id_Card_Arr = array(52);
            $id_Combo_Arr = array(58,59,63,64);
            if ($re) {
                $dataCard = array();
                $cart_idCard = array();
                $dataCombo = array();
                $cart_idCombo = array();
                while ($row = @mysql_fetch_assoc($re)) {
                    if(in_array($row['win_draw_gift_id'],$id_Card_Arr)){
                        #Card
                        $dataCard[$row['cart_id']] = $row;
                        $cart_idCard[$row['cart_id']] = $row['cart_id'];
                    }elseif(in_array($row['win_draw_gift_id'],$id_Combo_Arr)){
                        #Combo
                        $dataCombo[$row['cart_id']] = $row;
                        $cart_idCombo[$row['cart_id']] = $row['cart_id'];
                    }
                }

                $giftCode = array();
                $card = array();
                $cardToken = array();
                #Case Card
                if (!empty($cart_idCard)) {
                    $reg = DB::query("SELECT * FROM " . T_GIFT_CODE . " WHERE cart_id IN (" . implode(',', $cart_idCard) . ") AND status = 1");
                    if ($reg) {
                        while ($rowg = @mysql_fetch_assoc($reg)) {
                            $giftCode[$rowg['cart_id']] = $rowg;
                            $cardToken[$rowg['code']] = $rowg['code'];
                        }
                        if (!empty($cardToken)) {
                            $rec = DB::query("SELECT id,number,token,expired_time FROM " . T_CARD . " WHERE token IN ('" . implode("','", $cardToken) . "')");
                            if ($rec) {
                                while ($rowc = @mysql_fetch_assoc($rec)) {
                                    $card[$rowc['token']] = $rowc;
                                }
                            }
                        }
                    }
                }
                if (!empty($dataCard) && !empty($giftCode) && !empty($card)) {
                    foreach ($dataCard as $dt) {
                        if (isset($giftCode[$dt['cart_id']])) {
                            if (isset($card[$giftCode[$dt['cart_id']]['code']])) {
                                $update = array();
                                $number = $card[$giftCode[$dt['cart_id']]['code']]['number'];
                                $pin = $giftCode[$dt['cart_id']]['pin'];
                                $expired = $giftCode[$dt['cart_id']]['expired'];
                                $token = System::decrypt($card[$giftCode[$dt['cart_id']]['code']]['token']);
                                $link = Shorten::build(DOMAIN_LOYAL.'card/'.$token);
                                $expired_time= $card[$giftCode[$dt['cart_id']]['code']]['expired_time']>0?FunctionLib::dateFormat($card[$giftCode[$dt['cart_id']]['code']]['expired_time'],'d/m/Y'):'vô hạn';
                                $dtsms = [
                                    'amount' => FunctionLib::numberFormat($dt['amount']),
                                    'link' => $link,
                                    'pin' => $pin,
                                    'expired_time' => $expired_time,
                                    'fullname' => $dataCard[$dt['cart_id']]['fullname']
                                ];
                                $content = self::replaceData($dt['content'],$dtsms);
                                $phone = $dt['phone'];
                                $stringBody = '';
                                $curlHW = self::sendSmsFWS($content,$phone,$stringBody);
                                $update['data'] = $curlHW.$stringBody;
                                $update['process'] = 6;
                                $curlHWJson = json_decode($curlHW,true);
                                if ($curlHWJson['status'] != 1) {
                                    $update['process'] = 8;
                                }
                                DB::update('draw', $update, "id='" . $dt['id'] . "'");
                            }
                        }
                    }
                }
                #Case Combo
                if (!empty($cart_idCombo) && !empty($dataCombo)) {
                    $recc = DB::query("SELECT id,token FROM " . T_CART . " WHERE id IN (" . implode(',', $cart_idCombo) . ") AND status = 2 AND pay_status = 2");
                    if ($recc) {
                        while ($rowcc = @mysql_fetch_assoc($recc)) {
                            if (isset($dataCombo[$rowcc['id']])) {
                                $update = array();
                                $link = Shorten::build(DOMAIN_LOYAL.'combo/'.$rowcc['token']);
                                $dtsms = [
                                    'link' => $link,
                                    'fullname' => $dataCombo[$rowcc['id']]['fullname']
                                ];
                                $content = self::replaceData($dataCombo[$rowcc['id']]['content'],$dtsms);
                                $phone = $dataCombo[$rowcc['id']]['phone'];
                                $stringBody = '';
                                $curlHW = self::sendSmsFWS($content,$phone,$stringBody);
                                $update['data'] = $curlHW.$stringBody;
                                $update['process'] = 6;
                                $curlHWJson = json_decode($curlHW,true);
                                if ($curlHWJson['status'] != 1) {
                                    $update['process'] = 8;
                                }
                                DB::update('draw', $update, "id='" . $dataCombo[$rowcc['id']]['id'] . "'");
                            }
                        }
                    }
                }
            }
            FunctionLib::redisFlag('fwdSendSmsPush', 2);
            return System::apiSuccess($msg);
        }

    }

    private function replaceData($text = '', $data = array())
    {
        $matches = array();
        $pattern = "/\\{\\$\w+\\}/";
        preg_match_all($pattern, $text, $matches);

        if (!empty($matches[0])) {
            foreach ($matches[0] as $k => $v) {
                $keys = array();
                preg_match("/\\w+/", $v, $keys);
                if (!empty($keys)) {
                    $text = str_replace($v, $data[$keys[0]], $text);
                }
            }
        }
        $text = StringLib::post_db_parse_html($text);
        return $text;
    }

    function mb2022GenCardBalace()
    {

        if (!FunctionLib::redisFlag('mb2022GenCardBalace', 1)) {
            echo 'Da call';
            die;
        } else {
            $app_id = Url::getParamInt('app_id',500000181);
            $app_secret = Url::getParam('app_secret','5db3e8c4f1376cafa8c6077ca80daf1f');
            $re = DB::query("select * FROM draw where app_id=" . $app_id . " and process=3 AND status = 2 order by id ASC  limit 5");
            $msg = '';
            if ($re) {
                $genkeyCache = md5('DRAWGIFT' . $app_id);
                $drawGift = CacheLib::get($genkeyCache);
                if (empty($dataReturn)) {
                    $drawGift = DB::fetch_all("SELECT id,app_id,gift_detail_id,value FROM draw_gift WHERE app_id=" . $app_id . " AND status=2");
                    CacheLib::set($genkeyCache, $drawGift, 30 * 60 * 60);
                }
                while ($row = @mysql_fetch_assoc($re)) {
                    $dataBuy = array();
                    if (!empty($drawGift) && isset($drawGift[$row['win_draw_gift_id']])) {
                        $dataBuy[] = array('priceId' => $drawGift[$row['win_draw_gift_id']]['gift_detail_id'], 'quantity' => 1, 'amount' => $row['amount']);
                        $curl = new CURL();
                        $post = array(
                            'ttphone' => $row['phone'],
                            'ttfullname' => $row['fullname'],
                            'transaction_id' => 'MBAL-2022-BALACE-' . $row['id'],
                            'app_id' => $app_id,
                            'app_secret' => $app_secret,
                            'dataBuy' => json_encode($dataBuy)
                        );
                        $ex = $curl->post('https://api.urbox.vn/2.0/cart/cartPayVoucher', $post);
                        $response = json_decode($ex, true);
                        $update = array();
                        if ($response['done'] == 1) {
                            $update['process'] = 5;
                            $update['cart_id'] = $response['data']['cart']['id'];
                        } else {
                            $update['process'] = 7;
                        }
                        DB::update('draw', $update, "id='" . $row['id'] . "'");
                        $msg .= $response['msg'] . '<br/>';
                    }

                }
            }
            FunctionLib::redisFlag('mb2022GenCardBalace', 2);
            return System::apiSuccess($msg);
        }
    }

    function mb2022SendSmsPush()
    {
        if (!FunctionLib::redisFlag('mb2022SendSmsPush', 1)) {
            echo 'Da call';
            die;
        } else {
            $app_id = Url::getParamInt('app_id',500000181);
            $re = DB::query("select * FROM draw where app_id=" . $app_id . " and process=5 AND status = 2 order by id ASC limit 5");
            $msg = '';
            if ($re) {
                $data = array();
                $cart_id = array();
                while ($row = @mysql_fetch_assoc($re)) {
                    $data[$row['id']] = $row;
                    $cart_id[$row['cart_id']] = $row['cart_id'];
                }

                $giftCode = array();
                $card = array();
                $cardToken = array();
                if (!empty($cart_id)) {
                    $reg = DB::query("SELECT * FROM " . T_GIFT_CODE . " WHERE cart_id IN (" . implode(',', $cart_id) . ") AND status = 1");
                    if ($reg) {
                        while ($rowg = @mysql_fetch_assoc($reg)) {
                            $giftCode[$rowg['cart_id']] = $rowg;
                            $cardToken[$rowg['code']] = $rowg['code'];
                        }
                        if (!empty($cardToken)) {
                            $rec = DB::query("SELECT id,number,token,expired_time FROM " . T_CARD . " WHERE token IN ('" . implode("','", $cardToken) . "')");
                            if ($rec) {
                                while ($rowc = @mysql_fetch_assoc($rec)) {
                                    $card[$rowc['token']] = $rowc;
                                }
                            }
                        }
                    }

                }
                if (!empty($data) && !empty($giftCode) && !empty($card)) {
                    foreach ($data as $dt) {
                        if (isset($giftCode[$dt['cart_id']])) {
                            if (isset($card[$giftCode[$dt['cart_id']]['code']])) {
                                $number = $card[$giftCode[$dt['cart_id']]['code']]['number'];
                                $pin = $giftCode[$dt['cart_id']]['pin'];
                                $token = System::decrypt($card[$giftCode[$dt['cart_id']]['code']]['token']);
                                $link = Shorten::build('https://page.urbox.vn/card/'.$token);
                                $expired_time= $card[$giftCode[$dt['cart_id']]['code']]['expired_time']>0?FunctionLib::dateFormat($card[$giftCode[$dt['cart_id']]['code']]['expired_time'],'d/m/Y'):'vô hạn';
                                $dtsms = [
                                    'link' => $link,
                                    'expired_time' => $expired_time,
                                ];
                                $content = self::replaceData($dt['content'],$dtsms);
                                $phone = $dt['phone'];
                                $stringBody = '';
                                $curlHW = self::sendSmsMBal($content,$phone,$stringBody);
                                $update['data'] = $curlHW.$stringBody;
                                $update['process'] = 6;
                                $curlHWJson = json_decode($curlHW,true);
                                if ($curlHWJson['status'] != 1) {
                                    $update['process'] = 8;
                                }
                                DB::update('draw', $update, "id='" . $dt['id'] . "'");
                            }
                        }
                    }
                }
            }
            FunctionLib::redisFlag('mb2022SendSmsPush', 2);
            return System::apiSuccess($msg);
        }

    }

    function mb2022GameContra()
    {
        if (!FunctionLib::redisFlag('mb2022GameContra', 1)) {
            echo 'Da call';
            die;
        } else {
            $app_id = Url::getParamInt('app_id',548);
            $app_secret = Url::getParam('app_secret','fd73b6b19ae4484953af764680b7277a');
            $re = DB::query("select * FROM draw where app_id=" . $app_id . " and process=3 AND status = 2   order by id ASC  limit 5");
            $msg = '';
            if ($re) {
                $genkeyCache = md5('DRAWGIFT' . $app_id);
                $drawGift = CacheLib::get($genkeyCache);
                if (empty($dataReturn)) {
                    $drawGift = DB::fetch_all("SELECT id,app_id,gift_detail_id,value FROM draw_gift WHERE app_id=" . $app_id . " AND status=2");
                    CacheLib::set($genkeyCache, $drawGift, 30 * 60 * 60);
                }
                while ($row = @mysql_fetch_assoc($re)) {
                    $dataBuy = array();
                    if (!empty($drawGift) && isset($drawGift[$row['win_draw_gift_id']])) {
                        $dataBuy[] = array('priceId' => $drawGift[$row['win_draw_gift_id']]['gift_detail_id'], 'quantity' => 1, 'amount' => $row['amount']);
                        $curl = new CURL();
                        $post = array(
                            'site_user_id' => $row['phone'],
                            'ttphone' => $row['phone'],
                            'ttfullname' => $row['fullname'],
                            'transaction_id' => 'MBAL-2022-GAMECONTRA-' . $row['id'],
                            'app_id' => $app_id,
                            'app_secret' => $app_secret,
                            'isSendSms' => 1,
                            'dataBuy' => json_encode($dataBuy)
                        );
                        $ex = $curl->post('http://api.urbox.internal/2.0/cart/cartPayVoucher', $post);
                        $response = json_decode($ex, true);
                        $update = array();
                        if ($response['done'] == 1) {
                            $update['process'] = 5;
                            $update['cart_id'] = $response['data']['cart']['id'];
                        } else {
                            $update['process'] = 7;
                        }
                        DB::update('draw', $update, "id='" . $row['id'] . "'");
                        $msg .= $response['msg'] . '<br/>';
                    }

                }
            }
            FunctionLib::redisFlag('mb2022GameContra', 2);
            return System::apiSuccess($msg);
        }
    }

    function pvCombankCallInternal()
    {

        if (!FunctionLib::redisFlag('pvCombankCallInternal', 1)) {
            echo 'Da call';
            die;
        } else {
            $app_id = Url::getParamInt('app_id',*********);
            $app_secret = Url::getParam('app_secret','672255651b377d75674431735f361858');
            $re = DB::query("select * FROM draw where app_id=" . $app_id . " and process=3 AND status = 2 order by id ASC  limit 5");
            $msg = '';
            if ($re) {
                $genkeyCache = md5('DRAWGIFT' . $app_id);
                $drawGift = CacheLib::get($genkeyCache);
                if (empty($dataReturn)) {
                    $drawGift = DB::fetch_all("SELECT id,app_id,gift_detail_id,value FROM draw_gift WHERE app_id=" . $app_id . " AND status=2");
                    CacheLib::set($genkeyCache, $drawGift, 30 * 60 * 60);
                }
                while ($row = @mysql_fetch_assoc($re)) {
                    $dataBuy = array();
                    if (!empty($drawGift) && isset($drawGift[$row['win_draw_gift_id']])) {
                        $dataBuy[] = array('priceId' => $drawGift[$row['win_draw_gift_id']]['gift_detail_id'], 'quantity' => 1, 'amount' => $row['amount']);
                        $curl = new CURL();
                        $transaction_id = 'DRAW-'.FunctionLib::dateFormat(TIME_NOW,'Y').'-PVCOMBANK-' . $row['id'];
                        $post = array(
                            'site_user_id' => $row['indentity'],
                            'transaction_id' => $transaction_id,
                            'app_id' => $app_id,
                            'app_secret' => $app_secret,
                            'dataBuy' => json_encode($dataBuy)
                        );
                        $ex = $curl->post('https://api.urbox.internal/2.0/cart/cartPayVoucher', $post);
                        $response = json_decode($ex, true);
                        $update = array();
                        if ($response['done'] == 1) {
                            $update['process'] = 5;
                            $update['cart_id'] = $response['data']['cart']['id'];
                            $insertLoyal = array(
                                "app_id" => $app_id,
                                "campaign_id" => 9,
                                "site_user_id" => $row['indentity'],
                                "site_transaction_id" => $transaction_id,
                                "cart_id" => $response['data']['cart']['id'],
                                "point" => $response['data']['cart']['money_total'],
                                "exchange_rate" => 1,
                                "money" => $response['data']['cart']['money_total'],
                                "pay_status" => 3,
                                "created" => TIME_NOW
                            );
                            DB::insert(T_LOYAL_ORDER,$insertLoyal);
                        } else {
                            $update['process'] = 7;
                        }
                        DB::update('draw', $update, "id='" . $row['id'] . "'");
                        $msg .= $response['msg'] . '<br/>';
                    }

                }
            }
            FunctionLib::redisFlag('pvCombankCallInternal', 2);
            return System::apiSuccess($msg);
        }
    }

    function fwdSendSmsContent(){
        if(!FunctionLib::redisFlag('fwdSendSmsContent',1)){
            echo 'Da call';die;
        }else {
            $app_id = Url::getParamInt('app_id',403);
            $re = DB::query("select * FROM draw where  app_id=".$app_id." and process=9 AND status = 2 order by id ASC limit 20");
            $msg = '';
            if ($re) {
                while ($row = @mysql_fetch_assoc($re)) {
                    $stringBody = '';
                    $curlHW = self::sendSmsFWS($row['content'],$row['phone'],$stringBody);
                    $update['data'] = $curlHW.$stringBody;
                    $update['process'] = 10;
                    $curlHWJson = json_decode($curlHW,true);
                    if ($curlHWJson['status'] != 1) {
                        $update['process'] = 11;
                    }
                    DB::update('draw', $update, "id='" . $row['id'] . "'");
                }
            }
            FunctionLib::redisFlag('fwdSendSmsContent', 2);
            return System::apiSuccess($msg);
        }

    }

}