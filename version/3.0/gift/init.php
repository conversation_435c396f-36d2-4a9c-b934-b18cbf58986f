<?php

class initGift
{
    function __construct()
    {

    }

    function lists(){

        $id_gift_set = Url::getParamInt('giftSet', 393);//Mặc định sẽ lấy 1 gift set cho 1 danh sách
        $cat_id = Url::getParamInt('cat_id',0);
        $price_from = Url::getParamInt('price_from', 0);
        $reward_from = Url::getParamInt('reward_from', 0);
        $price_to = Url::getParamInt('price_to', 0);
        $reward_to = Url::getParamInt('reward_to', 0);
        $brand_id = Url::getParamInt('brand_id',0);
        $per_page = Url::getParamInt('per_page', 100);
        $sex = Url::getParamInt('sex', -1);

        $gift = Gift::get($id_gift_set);

        if(!empty($gift)){
            $re = DB::query("SELECT * FROM ".T_GIFT_SET." WHERE gift_id=".$gift['id']." ORDER BY sort ASC");
            $giftDetailId = array();
            $giftId = array();
            if($re){
                while ($row = @mysql_fetch_assoc($re)){
                    $giftDetailId[$row['gift_detail_id']] = $row['gift_detail_id'];
                    $data[$row['gift_detail_id']] = array('idset'=>$row['id'],'sort'=>$row['sort']);
                }
                if(!empty($giftDetailId)){
                    $res = DB::query("SELECT * FROM ".T_GIFT_DETAIL." WHERE status=1 AND id IN (".implode($giftDetailId,',').")");
                    if($res){
                        while ($row = @mysql_fetch_assoc($res)){
                            $giftId[$row['gift_id']] = $row['gift_id'];
                            $data[$row['id']] = array_merge($row,$data[$row['id']]);
                        }
                        if(!empty($giftId)){
                            $cond[] = " status IN (1,2)";
                            $cond[] = " id IN (".implode($giftId,',').")";
                            if($brand_id>0){
                                $cond[] = " brand_id =".$brand_id;
                            }
                            if($sex!=-1){
                                $cond[] = "sex >= ".$sex;
                            }
                            if ($reward_from > 0) {
                                $cond[] = "price_from >= ".$reward_from*CGlobal::$point_exchange;
                            }
                            if ($reward_to > 0) {
                                $cond[] = "price_to <= ".$reward_to*CGlobal::$point_exchange;
                            }
                            if($price_from>0){
                                $cond[] = "price_from >= ".$price_from;
                            }
                            if($price_to>0){
                                $cond[] = "price_to <= ".$price_to;
                            }
                            if($cat_id>0){
                                $cat = DB::query("SELECT * FROM ".T_CATEGORY." WHERE parent_id=".$cat_id);
                                $cat_id_arr[$cat_id] = $cat_id;
                                if($cat){
                                    while ($row = @mysql_fetch_assoc($cat)){
                                        $cat_id_arr[$row['id']] = $row['id'];
                                    }
                                }
                                $cond[] = " cat_id IN (".implode($cat_id_arr,',').")";
                            }
                            $sql = "SELECT * FROM ".T_GIFT." WHERE ".FunctionLib::addCondition($cond);

                            $reg = Pagging::pager_query($sql,$per_page);
                            $arrBrandID = array();
                            while ($row = @mysql_fetch_assoc($reg)){
                                $arrBrandID[$row['brand_id']] = $row['brand_id'];
                                foreach ($data as $k){
                                    if(isset($k['gift_id'])&&$row['id']==$k['gift_id']){
                                        $row['detail'] =  array('id'=>$k['id'],'title'=>$k['title'],'price'=>$k['price'],'price_format'=>FunctionLib::priceFormat($k['price'],'VNĐ'),'point'=>Gift::exchangePoint($k['price']));;
                                        $row['sort'] = $k['sort'];
                                    }
                                }
                                $arrImage = (array)json_decode($row['images'], true);
                                $temp_imgages = '';
                                if(!empty($arrImage)){
                                    foreach ($arrImage as $k => $v) {
                                        if($v!=''&&$temp_imgages == ''){
                                            $temp_imgages = ImageUrl::getImageURL($v, $row['created'], 320, 'gift', 'gift/');
                                        }
                                    }
                                }
                                $temp = array(
                                    'id'=>$row['id'],
                                    'brand_id'=>$row['brand_id'],
                                    'name'=>$row['name'],
                                    'images_src'=>$temp_imgages,
                                    'sort'=>isset($row['sort'])?$row['sort']:0,
                                    'detail'=>isset($row['detail'])?$row['detail']:array(),
                                );
                                $giftSet[$row['id']] = $temp;
                            }
                            if(!empty($arrBrandID)){
                                $arrBrand = Brand::getByID($arrBrandID);
                            }

                            foreach ($giftSet as $k => $v) {
                                if(!empty($arrBrand[$v['brand_id']])){
                                    $giftSet[$k]['brand'] = array('id'=>$arrBrand[$v['brand_id']]['id'],'title'=>$arrBrand[$v['brand_id']]['title']);
                                }
                            }
                            if(!empty($giftSet)){
                                usort($giftSet, "self::cmp");

                            }
                            return System::apiSuccess(array('items'=>array_values($giftSet),'totalPage'=>Pagging::$totalPage));
                        }

                    }
                }
            }

        }

        return System::apiError('Không tìm thấy sản phẩm');

    }

    static function cmp($a, $b)
    {
        return strcmp($a["sort"], $b["sort"]);
    }


    function listsAll()
    {

        $brand_id = Url::getParamInt('brand_id', 0);
        $cat_id = Url::getParamInt('cat_id', 0);
        $keyword = Url::getParam('keyword', '');
        $is_hot = Url::getParamInt('is_hot', 0);
        $per_page = Url::getParamInt('per_page', 0);
        $sex = Url::getParamInt('sex', -1);
        $price_from = Url::getParamInt('price_from', 0);
        $reward_from = Url::getParamInt('reward_from', 0);
        $price_to = Url::getParamInt('price_to', 0);
        $reward_to = Url::getParamInt('reward_to', 0);

        $param['status'] = 1;
        if ($cat_id > 0) {
            $param['cat_id'] = $cat_id;
        }
        if ($sex != -1) {
            $param['sex'] = $sex;
        }
        if ($price_from > 0) {
            $param['price_from'] = $price_from;
        }
        if ($price_to > 0) {
            $param['price_to'] = $price_to;
        }
        if ($reward_from > 0) {
            $param['price_from'] = $reward_from*CGlobal::$point_exchange;
        }
        if ($reward_to > 0) {
            $param['price_to'] = $reward_to*CGlobal::$point_exchange;
        }
        if ($keyword !='') {
            $param['keyword'] = $keyword;
        }
        if ($is_hot == 1) {
            $param['display'] = 1;
        }
        if ($brand_id > 0) {
            $param['brand_id'] = $brand_id;
        }
        $data = Gift::lists($param,false,$per_page);

        return System::apiSuccess(array('items'=>array_values($data),'totalPage'=>Pagging::$totalPage));

    }

    function detail()
    {
        $id = Url::getParamInt('id', 0);
        if($id>0){
            $gift = DB::fetch("SELECT id,name,images,created,article_id,brand_id FROM ".T_GIFT." WHERE status=1 AND id=".$id);
            if(!empty($gift)){
                $arrImage = (array)json_decode($gift['images'], true);
                if(!empty($arrImage)){
                    foreach ($arrImage as $k => $v) {
                        if($v!=''&&!isset($gift['image_src'])){
                            $gift['image_src'] = ImageUrl::getImageURL($v, $gift['created'], 320, 'gift', 'gift/');
                        }

                    }
                }
                $gift['article'] = DB::fetch("SELECT description FROM ".T_ARTICLE." WHERE id=".$gift['article_id']);
                $re =  DB::query("SELECT * FROM ".T_GIFT_DETAIL." WHERE gift_id=".$gift['id']);
                if($re){
                    while ($row = @mysql_fetch_assoc($re)){
                        $gift['detail'][] = array('id'=>$row['id'],'title'=>$row['title'],'price'=>$row['price'],'price_format'=>FunctionLib::priceFormat($row['price'],'VNĐ'),'point'=>Gift::exchangePoint($row['price']));
                    }
                }
                $gift['brand'] = DB::fetch("SELECT title,image,created FROM ".T_BRAND." WHERE id=".$gift['brand_id']);
                if(!empty($gift['brand'])){
                    $gift['brand']['images'] = ImageUrl::getImageURL($gift['brand']['image'], $gift['brand']['created'], 160, 'brand' , 'brand/');
                }
                unset($gift['article_id']);
                unset($gift['images']);
                unset($gift['brand_id']);
                return System::apiSuccess($gift);
            }
        }
        return System::apiError('notParam');
    }

    function filter(){
        $dataFiter = array();
        #Chủ đề
        $menu_cd = Category::getChild(26);
        $menu_qt = Category::getChild(2);
        $menu = array_merge($menu_qt,$menu_cd);
        $dataFiter['CATEGORIES'] = array(
            'key'=>'cat_id',
            'title'=>'Chủ đề',
            'value'=>$menu
        );
        #Thương hiêu
        $brand = DB::fetch_all("SELECT id,title FROM ".T_BRAND." WHERE status=".IS_HOT." ORDER BY is_hot DESC");
        $dataFiter['BRANDS'] = array(
            'key'=>'brand_id',
            'title'=>'Thương hiệu',
            'value'=> array_values($brand)
        );
        #Gioi tinh
        $dataFiter['SEX'] = array(
            'key'=>'sex',
            'title'=>'Giới tính',
            'value'=> array(
                array('id'=>0,'title'=>'Nữ'),
                array('id'=>1,'title'=>'Nam'),
                array('id'=>-1,'title'=>'Cả hai')
            )
        );

        return System::apiSuccess(
            array(
                'items'=>$dataFiter
            )
        );
    }


}