<?php
class XRequestID {

    /**
     * @return string
     */
    public static function generateRequestId()
    {
        return time() . uniqid();
    }

    /**
     * @param $requestName
     * @param $value
     */
    public function assignRequestId($requestName, $value)
    {
        header($requestName . ': ' . $value);
    }

    /**
     * @param $requestName
     *
     * @return bool
     */
    public function isRequestValid($requestName)
    {
        $headers = getallheaders();
        return isset($headers[$requestName]);
    }

    public function validate($requestName = 'X-Request-Id')
    {
        if (!self::isRequestValid($requestName)) {
            throw new ErrorException('Request Id không hợp lệ', 400);
        }
        $allHeaders = getallheaders();
        $value = $allHeaders[$requestName];
        self::assignRequestId($requestName, $value);
    }
}
?>